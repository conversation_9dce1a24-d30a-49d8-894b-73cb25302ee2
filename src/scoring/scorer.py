"""
Verification Scorer for calculating confidence scores and color classifications
"""
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from loguru import logger

from ..models import (
    VerificationResult,
    OverallVerificationResult,
    ColorCode,
    ValidationMatrix,
    ValidationRule
)
from config.settings import verification_rules


class VerificationScorer:
    """
    Handles scoring, color classification, and human review flagging
    """
    
    def __init__(self):
        self.default_weights = verification_rules.FIELD_WEIGHTS
        self.color_thresholds = verification_rules.COLOR_THRESHOLDS
        self.field_rules = verification_rules.FIELD_RULES
    
    def calculate_field_score(
        self,
        expected_value: Any,
        actual_value: Any,
        validation_rule: ValidationRule,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[float, ColorCode, str]:
        """
        Calculate score, color code, and remark for a single field
        
        Returns:
            Tuple of (confidence_score, color_code, remark)
        """
        try:
            # Handle missing values
            if expected_value is None and actual_value is None:
                return 1.0, ColorCode.GREEN, "Both values are empty (acceptable)"
            
            if expected_value is None:
                if validation_rule.is_required:
                    return 0.0, ColorCode.RED, "Expected value is required but not provided"
                else:
                    return 0.8, ColorCode.AMBER, "Expected value not specified"
            
            if actual_value is None:
                if validation_rule.is_required:
                    return 0.0, ColorCode.RED, "Actual value is missing"
                else:
                    return 0.5, ColorCode.AMBER, "Actual value not provided"
            
            # Calculate base score based on field type and matching strategy
            base_score = self._calculate_base_score(
                expected_value, actual_value, validation_rule, context
            )
            
            # Apply field-specific adjustments
            adjusted_score = self._apply_field_adjustments(
                base_score, validation_rule, context
            )
            
            # Determine color code
            color_code = self._get_color_code(adjusted_score, validation_rule)
            
            # Generate remark
            remark = self._generate_remark(
                expected_value, actual_value, adjusted_score, validation_rule, context
            )
            
            return adjusted_score, color_code, remark
            
        except Exception as e:
            logger.error(f"Error calculating field score: {str(e)}")
            return 0.0, ColorCode.RED, f"Scoring error: {str(e)}"
    
    def calculate_overall_score(
        self,
        field_results: List[VerificationResult],
        scoring_weights: Optional[Dict[str, float]] = None,
        validation_matrix: Optional[ValidationMatrix] = None
    ) -> Tuple[float, ColorCode, bool]:
        """
        Calculate overall verification score
        
        Returns:
            Tuple of (overall_score, overall_color_code, requires_human_review)
        """
        if not field_results:
            return 0.0, ColorCode.RED, True
        
        # Use provided weights or defaults
        weights = scoring_weights or self.default_weights
        
        # Calculate weighted score
        total_weighted_score = 0.0
        total_weight = 0.0
        
        # Group results by category
        categorized_results = self._categorize_field_results(field_results)
        
        for category, results in categorized_results.items():
            if not results:
                continue
                
            # Calculate category score
            category_score = sum(r.confidence_score for r in results) / len(results)
            category_weight = weights.get(category, 0.0)
            
            total_weighted_score += category_score * category_weight
            total_weight += category_weight
        
        # Handle case where no weights match
        if total_weight == 0:
            overall_score = sum(r.confidence_score for r in field_results) / len(field_results)
        else:
            overall_score = total_weighted_score / total_weight
        
        # Apply critical field penalties
        overall_score = self._apply_critical_field_penalties(
            overall_score, field_results, validation_matrix
        )
        
        # Determine overall color code
        overall_color_code = self._get_overall_color_code(overall_score, validation_matrix)
        
        # Determine if human review is required
        requires_human_review = self._requires_human_review(
            overall_score, field_results, validation_matrix
        )
        
        return overall_score, overall_color_code, requires_human_review
    
    def _calculate_base_score(
        self,
        expected_value: Any,
        actual_value: Any,
        validation_rule: ValidationRule,
        context: Optional[Dict[str, Any]]
    ) -> float:
        """Calculate base score before adjustments"""
        
        # Exact match check
        if str(expected_value).strip().lower() == str(actual_value).strip().lower():
            return 1.0
        
        # Field-specific scoring logic
        field_type = validation_rule.field_type.value
        
        if field_type == "text":
            return self._score_text_field(expected_value, actual_value, validation_rule)
        elif field_type == "date":
            return self._score_date_field(expected_value, actual_value, validation_rule)
        elif field_type == "number":
            return self._score_number_field(expected_value, actual_value, validation_rule)
        elif field_type == "email":
            return self._score_email_field(expected_value, actual_value, validation_rule)
        elif field_type == "phone":
            return self._score_phone_field(expected_value, actual_value, validation_rule)
        else:
            # Default to text scoring
            return self._score_text_field(expected_value, actual_value, validation_rule)
    
    def _score_text_field(self, expected: str, actual: str, rule: ValidationRule) -> float:
        """Score text fields using similarity"""
        from difflib import SequenceMatcher
        
        expected_norm = str(expected).strip().lower()
        actual_norm = str(actual).strip().lower()
        
        similarity = SequenceMatcher(None, expected_norm, actual_norm).ratio()
        
        threshold = rule.similarity_threshold or 0.8
        if similarity >= threshold:
            return similarity
        else:
            # Penalize below threshold
            return similarity * 0.7
    
    def _score_date_field(self, expected, actual, rule: ValidationRule) -> float:
        """Score date fields with tolerance"""
        from datetime import date, datetime
        
        try:
            if isinstance(expected, str):
                expected_date = datetime.fromisoformat(expected).date()
            else:
                expected_date = expected
                
            if isinstance(actual, str):
                actual_date = datetime.fromisoformat(actual).date()
            else:
                actual_date = actual
            
            diff_days = abs((expected_date - actual_date).days)
            tolerance = rule.date_tolerance_days or 30
            
            if diff_days == 0:
                return 1.0
            elif diff_days <= tolerance:
                return 1.0 - (diff_days / tolerance) * 0.3
            else:
                # Exponential decay beyond tolerance
                excess_ratio = (diff_days - tolerance) / tolerance
                penalty = min(0.7, excess_ratio * 0.4)
                return max(0.0, 0.7 - penalty)
                
        except Exception:
            return 0.0
    
    def _score_number_field(self, expected: float, actual: float, rule: ValidationRule) -> float:
        """Score numeric fields with tolerance"""
        try:
            expected_val = float(expected)
            actual_val = float(actual)
            
            if expected_val == actual_val:
                return 1.0
            
            # Calculate percentage difference
            if expected_val != 0:
                percent_diff = abs((actual_val - expected_val) / expected_val) * 100
            else:
                return 0.0 if actual_val != 0 else 1.0
            
            tolerance = rule.numeric_tolerance_percent or 10.0
            
            if percent_diff <= tolerance:
                return 1.0 - (percent_diff / tolerance) * 0.2
            else:
                # Penalty beyond tolerance
                excess_ratio = (percent_diff - tolerance) / tolerance
                penalty = min(0.8, excess_ratio * 0.4)
                return max(0.0, 0.8 - penalty)
                
        except Exception:
            return 0.0
    
    def _score_email_field(self, expected: str, actual: str, rule: ValidationRule) -> float:
        """Score email fields (usually exact match required)"""
        expected_norm = str(expected).strip().lower()
        actual_norm = str(actual).strip().lower()
        
        return 1.0 if expected_norm == actual_norm else 0.0
    
    def _score_phone_field(self, expected: str, actual: str, rule: ValidationRule) -> float:
        """Score phone fields with normalization"""
        import re
        
        # Normalize phone numbers
        expected_norm = re.sub(r'[\s\-\(\)\+]', '', str(expected))
        actual_norm = re.sub(r'[\s\-\(\)\+]', '', str(actual))
        
        # Compare last 10 digits if both are long enough
        if len(expected_norm) >= 10 and len(actual_norm) >= 10:
            expected_core = expected_norm[-10:]
            actual_core = actual_norm[-10:]
            return 1.0 if expected_core == actual_core else 0.0
        else:
            return 1.0 if expected_norm == actual_norm else 0.0
    
    def _apply_field_adjustments(
        self,
        base_score: float,
        validation_rule: ValidationRule,
        context: Optional[Dict[str, Any]]
    ) -> float:
        """Apply field-specific adjustments to base score"""
        
        adjusted_score = base_score
        
        # Apply priority-based adjustments
        if validation_rule.priority == "critical":
            # Critical fields have stricter scoring
            if adjusted_score < 0.9:
                adjusted_score *= 0.8
        elif validation_rule.priority == "low":
            # Low priority fields are more lenient
            if adjusted_score > 0.5:
                adjusted_score = min(1.0, adjusted_score * 1.1)
        
        # Apply context-based adjustments
        if context:
            # Government verification bonus
            if context.get("government_verified", False):
                adjusted_score = min(1.0, adjusted_score * 1.1)
            
            # Multiple source confirmation bonus
            if context.get("multiple_sources", False):
                adjusted_score = min(1.0, adjusted_score * 1.05)
        
        return adjusted_score
    
    def _get_color_code(self, score: float, validation_rule: ValidationRule) -> ColorCode:
        """Determine color code based on score and rule thresholds"""
        
        green_threshold = validation_rule.green_threshold
        amber_threshold = validation_rule.amber_threshold
        
        if score >= green_threshold:
            return ColorCode.GREEN
        elif score >= amber_threshold:
            return ColorCode.AMBER
        else:
            return ColorCode.RED
    
    def _get_overall_color_code(
        self,
        overall_score: float,
        validation_matrix: Optional[ValidationMatrix]
    ) -> ColorCode:
        """Determine overall color code"""
        
        if validation_matrix:
            green_threshold = validation_matrix.global_green_threshold
            amber_threshold = validation_matrix.global_amber_threshold
        else:
            green_threshold = self.color_thresholds["green"]
            amber_threshold = self.color_thresholds["amber"]
        
        if overall_score >= green_threshold:
            return ColorCode.GREEN
        elif overall_score >= amber_threshold:
            return ColorCode.AMBER
        else:
            return ColorCode.RED

    def _categorize_field_results(
        self,
        field_results: List[VerificationResult]
    ) -> Dict[str, List[VerificationResult]]:
        """Categorize field results by type"""

        categories = {
            "employment": [],
            "education": [],
            "personal_info": [],
            "other": []
        }

        for result in field_results:
            field_name = result.field_name.lower()

            if any(keyword in field_name for keyword in ["employment", "employer", "job", "salary", "work"]):
                categories["employment"].append(result)
            elif any(keyword in field_name for keyword in ["education", "degree", "institution", "school", "university"]):
                categories["education"].append(result)
            elif any(keyword in field_name for keyword in ["name", "id", "email", "phone", "address", "personal"]):
                categories["personal_info"].append(result)
            else:
                categories["other"].append(result)

        return categories

    def _apply_critical_field_penalties(
        self,
        overall_score: float,
        field_results: List[VerificationResult],
        validation_matrix: Optional[ValidationMatrix]
    ) -> float:
        """Apply penalties for critical field failures"""

        if not validation_matrix:
            return overall_score

        critical_fields = validation_matrix.get_critical_fields()

        for result in field_results:
            # Check if this is a critical field
            is_critical = any(
                critical_field in result.field_name
                for critical_field in critical_fields
            )

            if is_critical and result.color_code == ColorCode.RED:
                # Apply significant penalty for critical field failure
                overall_score *= 0.7
            elif is_critical and result.color_code == ColorCode.AMBER:
                # Apply moderate penalty for critical field issues
                overall_score *= 0.9

        return overall_score

    def _requires_human_review(
        self,
        overall_score: float,
        field_results: List[VerificationResult],
        validation_matrix: Optional[ValidationMatrix]
    ) -> bool:
        """Determine if human review is required"""

        # Check overall score threshold
        if validation_matrix:
            threshold = validation_matrix.human_review_threshold
        else:
            threshold = 0.6

        if overall_score < threshold:
            return True

        # Check for any field requiring human review
        if any(result.requires_human_review for result in field_results):
            return True

        # Check for critical field failures
        if validation_matrix:
            critical_fields = validation_matrix.get_critical_fields()
            for result in field_results:
                is_critical = any(
                    critical_field in result.field_name
                    for critical_field in critical_fields
                )
                if is_critical and result.color_code == ColorCode.RED:
                    return True

        # Check for multiple amber/red flags
        amber_red_count = sum(
            1 for result in field_results
            if result.color_code in [ColorCode.AMBER, ColorCode.RED]
        )

        if amber_red_count >= 3:  # Configurable threshold
            return True

        return False

    def _generate_remark(
        self,
        expected_value: Any,
        actual_value: Any,
        score: float,
        validation_rule: ValidationRule,
        context: Optional[Dict[str, Any]]
    ) -> str:
        """Generate human-readable remark for the verification result"""

        field_name = validation_rule.field_name.replace('_', ' ').title()

        if score >= validation_rule.green_threshold:
            if score == 1.0:
                return f"{field_name} matches exactly"
            else:
                return f"{field_name} matches with high confidence ({score:.2f})"

        elif score >= validation_rule.amber_threshold:
            if validation_rule.field_type.value == "date":
                return f"{field_name} is within acceptable tolerance"
            elif validation_rule.field_type.value == "number":
                return f"{field_name} is close to expected value"
            else:
                return f"{field_name} has minor differences but is likely correct"

        else:
            if score == 0.0:
                if actual_value is None:
                    return f"{field_name} is missing"
                else:
                    return f"{field_name} does not match expected value"
            else:
                return f"{field_name} has significant discrepancies (confidence: {score:.2f})"

    def generate_verification_summary(
        self,
        overall_result: OverallVerificationResult,
        validation_matrix: Optional[ValidationMatrix] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive verification summary"""

        field_results = overall_result.field_results

        # Count results by color
        color_counts = {
            "green": sum(1 for r in field_results if r.color_code == ColorCode.GREEN),
            "amber": sum(1 for r in field_results if r.color_code == ColorCode.AMBER),
            "red": sum(1 for r in field_results if r.color_code == ColorCode.RED)
        }

        # Identify critical issues
        critical_issues = [
            r for r in field_results
            if r.color_code == ColorCode.RED and r.requires_human_review
        ]

        # Calculate risk level
        risk_level = self._calculate_risk_level(overall_result.overall_score, color_counts)

        # Generate recommendations
        recommendations = self._generate_recommendations(
            overall_result, validation_matrix, critical_issues
        )

        return {
            "overall_score": overall_result.overall_score,
            "overall_color_code": overall_result.overall_color_code.value,
            "risk_level": risk_level,
            "field_summary": {
                "total_fields": len(field_results),
                "green_count": color_counts["green"],
                "amber_count": color_counts["amber"],
                "red_count": color_counts["red"]
            },
            "critical_issues": [
                {
                    "field": issue.field_name,
                    "issue": issue.remark,
                    "expected": issue.expected_value,
                    "actual": issue.actual_value
                }
                for issue in critical_issues
            ],
            "requires_human_review": overall_result.requires_human_review,
            "recommendations": recommendations,
            "processing_time": overall_result.processing_time_seconds
        }

    def _calculate_risk_level(self, overall_score: float, color_counts: Dict[str, int]) -> str:
        """Calculate risk level based on score and color distribution"""

        if overall_score >= 0.9 and color_counts["red"] == 0:
            return "Low"
        elif overall_score >= 0.7 and color_counts["red"] <= 1:
            return "Medium"
        elif overall_score >= 0.5:
            return "High"
        else:
            return "Critical"

    def _generate_recommendations(
        self,
        overall_result: OverallVerificationResult,
        validation_matrix: Optional[ValidationMatrix],
        critical_issues: List[VerificationResult]
    ) -> Dict[str, Any]:
        """Generate actionable recommendations"""

        recommendations = {
            "hiring_decision": "Recommend",
            "additional_verification": [],
            "immediate_actions": [],
            "notes": []
        }

        # Determine hiring decision
        if overall_result.overall_score >= 0.9:
            recommendations["hiring_decision"] = "Recommend"
        elif overall_result.overall_score >= 0.7:
            recommendations["hiring_decision"] = "Conditional"
            recommendations["notes"].append("Consider additional verification for amber flags")
        else:
            recommendations["hiring_decision"] = "Not Recommend"
            recommendations["notes"].append("Significant verification issues found")

        # Additional verification recommendations
        if critical_issues:
            for issue in critical_issues:
                if "employment" in issue.field_name:
                    recommendations["additional_verification"].append("Employment reference check")
                elif "education" in issue.field_name:
                    recommendations["additional_verification"].append("Education credential verification")
                elif "id" in issue.field_name or "name" in issue.field_name:
                    recommendations["additional_verification"].append("Identity document verification")

        # Immediate actions
        if overall_result.requires_human_review:
            recommendations["immediate_actions"].append("Schedule human review")

        if len(critical_issues) > 0:
            recommendations["immediate_actions"].append("Investigate critical discrepancies")

        return recommendations
