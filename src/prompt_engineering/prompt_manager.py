"""
Prompt Engineering Manager for structured verification prompts
"""
import os
import sys
import json
from typing import Dict, Any, Optional, List
from datetime import date, datetime
from jinja2 import Template
from loguru import logger

# Add src to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import (
    EnrichedCPF,
    ClientInput,
    ValidationMatrix,
    EmploymentRecord,
    EducationRecord,
    ValidationRule
)


class PromptManager:
    """
    Manages structured prompts for different verification scenarios
    """
    
    def __init__(self):
        self.templates = self._load_prompt_templates()
    
    def _load_prompt_templates(self) -> Dict[str, Template]:
        """Load and compile prompt templates"""
        templates = {}
        
        # Employment verification prompt
        templates['employment_verification'] = Template("""
You are an expert background verification analyst. Your task is to compare candidate employment information with expected values and provide a detailed assessment.

**CANDIDATE EMPLOYMENT INFORMATION:**
- Employer: {{ actual_employment.employer_name }}
- Job Title: {{ actual_employment.job_title }}
- Start Date: {{ actual_employment.start_date }}
- End Date: {{ actual_employment.end_date or "Current" }}
- Salary: {{ actual_employment.salary }} {{ actual_employment.currency }}
- Location: {{ actual_employment.location }}

**EXPECTED EMPLOYMENT INFORMATION:**
- Expected Employer: {{ expected_employment.employer_name }}
- Expected Job Title: {{ expected_employment.job_title or "Not specified" }}
- Expected Start Date: {{ expected_employment.start_date or "Not specified" }}
- Expected End Date: {{ expected_employment.end_date or "Not specified" }}
- Expected Salary Range: {{ expected_employment.min_salary }}-{{ expected_employment.max_salary }} {{ expected_employment.currency }}
- Expected Location: {{ expected_employment.expected_location or "Not specified" }}

**VALIDATION RULES:**
- Employer Name Matching: {{ validation_rules.employer_name_rule.match_type }} (Threshold: {{ validation_rules.employer_name_rule.similarity_threshold }})
- Date Tolerance: {{ validation_rules.start_date_rule.date_tolerance_days }} days
- Salary Tolerance: {{ validation_rules.salary_rule.numeric_tolerance_percent }}%

**ANALYSIS REQUIRED:**
1. Compare employer names and assess similarity
2. Verify employment dates within tolerance
3. Check salary against expected range
4. Evaluate location match
5. Identify any red flags or inconsistencies

**OUTPUT FORMAT:**
Provide your analysis in the following JSON format:
{
    "employer_match": {
        "score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "date_verification": {
        "start_date_score": 0.0-1.0,
        "end_date_score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "salary_verification": {
        "score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "overall_assessment": {
        "confidence_score": 0.0-1.0,
        "color_code": "Green|Amber|Red",
        "summary": "brief summary",
        "red_flags": ["list of concerns"],
        "requires_human_review": true/false
    }
}
""")

        # Education verification prompt
        templates['education_verification'] = Template("""
You are an expert background verification analyst. Your task is to compare candidate education information with expected values.

**CANDIDATE EDUCATION INFORMATION:**
- Institution: {{ actual_education.institution_name }}
- Degree: {{ actual_education.degree }}
- Field of Study: {{ actual_education.field_of_study }}
- Start Date: {{ actual_education.start_date }}
- End Date: {{ actual_education.end_date }}
- Grade: {{ actual_education.grade }}
- Location: {{ actual_education.location }}

**EXPECTED EDUCATION INFORMATION:**
- Expected Institution: {{ expected_education.institution_name }}
- Expected Degree: {{ expected_education.degree }}
- Expected Field: {{ expected_education.field_of_study or "Not specified" }}
- Expected Graduation: {{ expected_education.graduation_date or "Not specified" }}
- Minimum Grade: {{ expected_education.min_grade or "Not specified" }}

**VALIDATION RULES:**
- Institution Matching: {{ validation_rules.institution_name_rule.match_type }}
- Degree Matching: {{ validation_rules.degree_rule.match_type }}
- Accept Equivalent Degrees: {{ validation_rules.accept_equivalent_degrees }}

**ANALYSIS REQUIRED:**
1. Compare institution names and assess similarity
2. Verify degree matches or is equivalent
3. Check field of study alignment
4. Validate graduation dates
5. Assess grade requirements

**OUTPUT FORMAT:**
Provide your analysis in JSON format:
{
    "institution_match": {
        "score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "degree_verification": {
        "score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "overall_assessment": {
        "confidence_score": 0.0-1.0,
        "color_code": "Green|Amber|Red",
        "summary": "brief summary",
        "requires_human_review": true/false
    }
}
""")

        # Personal information verification prompt
        templates['personal_info_verification'] = Template("""
You are an expert background verification analyst. Your task is to verify personal information accuracy.

**CANDIDATE PERSONAL INFORMATION:**
- Full Name: {{ actual_info.full_name }}
- Date of Birth: {{ actual_info.date_of_birth }}
- National ID: {{ actual_info.national_id }}
- Email: {{ actual_info.email }}
- Phone: {{ actual_info.phone }}
- Address: {{ actual_info.current_address }}

**EXPECTED PERSONAL INFORMATION:**
- Expected Name: {{ expected_info.full_name }}
- Alternative Names: {{ expected_info.alternative_names }}
- Expected DOB: {{ expected_info.date_of_birth }}
- Expected ID: {{ expected_info.national_id }}
- Expected Email: {{ expected_info.email }}
- Expected Phone: {{ expected_info.phone }}

**GOVERNMENT VERIFICATION STATUS:**
- National ID Verified: {{ government_verification.national_id_verified }}
- Address Verified: {{ government_verification.address_verified }}
- Verification Date: {{ government_verification.verification_date }}

**ANALYSIS REQUIRED:**
1. Compare names including variations and aliases
2. Verify identification numbers
3. Check contact information accuracy
4. Assess government verification status
5. Identify potential identity issues

**OUTPUT FORMAT:**
{
    "name_verification": {
        "score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "id_verification": {
        "score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "contact_verification": {
        "score": 0.0-1.0,
        "explanation": "detailed explanation"
    },
    "overall_assessment": {
        "confidence_score": 0.0-1.0,
        "color_code": "Green|Amber|Red",
        "summary": "brief summary",
        "requires_human_review": true/false
    }
}
""")

        # Comprehensive verification prompt
        templates['comprehensive_verification'] = Template("""
You are a senior background verification analyst conducting a comprehensive review.

**VERIFICATION SUMMARY:**
- Candidate ID: {{ candidate_id }}
- Total Fields Verified: {{ total_fields }}
- Employment Records: {{ employment_count }}
- Education Records: {{ education_count }}
- Government Verification Status: {{ government_verified_fields }}

**FIELD VERIFICATION RESULTS:**
{% for result in field_results %}
- {{ result.field_name }}: {{ result.color_code }} (Score: {{ result.confidence_score }})
  Remark: {{ result.remark }}
{% endfor %}

**CLIENT REQUIREMENTS:**
- Priority Level: {{ client_priority }}
- Critical Verifications: {{ critical_verifications }}
- Compliance Requirements: {{ compliance_requirements }}

**ANALYSIS REQUIRED:**
1. Assess overall verification quality
2. Identify patterns or inconsistencies
3. Evaluate risk level
4. Determine if additional verification needed
5. Provide hiring recommendation

**OUTPUT FORMAT:**
{
    "overall_assessment": {
        "confidence_score": 0.0-1.0,
        "color_code": "Green|Amber|Red",
        "risk_level": "Low|Medium|High|Critical"
    },
    "key_findings": {
        "strengths": ["list of positive findings"],
        "concerns": ["list of concerns"],
        "critical_issues": ["list of critical issues"]
    },
    "recommendations": {
        "hiring_decision": "Recommend|Conditional|Not Recommend",
        "additional_verification": ["list of additional checks needed"],
        "human_review_required": true/false
    },
    "summary": "comprehensive summary for decision makers"
}
""")

        return templates
    
    def generate_employment_verification_prompt(
        self,
        actual_employment: EmploymentRecord,
        expected_employment: Any,
        validation_rules: Any
    ) -> str:
        """Generate employment verification prompt"""
        
        template = self.templates['employment_verification']
        return template.render(
            actual_employment=actual_employment,
            expected_employment=expected_employment,
            validation_rules=validation_rules
        )
    
    def generate_education_verification_prompt(
        self,
        actual_education: EducationRecord,
        expected_education: Any,
        validation_rules: Any
    ) -> str:
        """Generate education verification prompt"""
        
        template = self.templates['education_verification']
        return template.render(
            actual_education=actual_education,
            expected_education=expected_education,
            validation_rules=validation_rules
        )
    
    def generate_personal_info_verification_prompt(
        self,
        actual_info: Any,
        expected_info: Any,
        government_verification: Any
    ) -> str:
        """Generate personal information verification prompt"""
        
        template = self.templates['personal_info_verification']
        return template.render(
            actual_info=actual_info,
            expected_info=expected_info,
            government_verification=government_verification
        )
    
    def generate_comprehensive_verification_prompt(
        self,
        candidate_id: str,
        field_results: List[Any],
        client_requirements: Dict[str, Any],
        verification_summary: Dict[str, Any]
    ) -> str:
        """Generate comprehensive verification prompt"""
        
        template = self.templates['comprehensive_verification']
        return template.render(
            candidate_id=candidate_id,
            total_fields=len(field_results),
            employment_count=verification_summary.get('employment_count', 0),
            education_count=verification_summary.get('education_count', 0),
            government_verified_fields=verification_summary.get('government_verified_fields', {}),
            field_results=field_results,
            client_priority=client_requirements.get('priority_level', 'medium'),
            critical_verifications=client_requirements.get('critical_verifications', []),
            compliance_requirements=client_requirements.get('compliance_requirements', [])
        )
