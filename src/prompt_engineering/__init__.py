"""
Prompt engineering framework for structured verification analysis
"""

from .prompt_manager import Prompt<PERSON>anager
from .bedrock_client import BedrockClient

# Keep LLMClient for backward compatibility if it exists
try:
    from .llm_client import LLMClient
    __all__ = [
        "PromptManager",
        "BedrockClient",
        "LLMClient"
    ]
except ImportError:
    __all__ = [
        "PromptManager",
        "BedrockClient"
    ]