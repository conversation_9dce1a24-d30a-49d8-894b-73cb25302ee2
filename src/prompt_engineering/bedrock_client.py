"""
AWS Bedrock Client for interacting with Mistral 7B Instruct model
"""
import json
import asyncio
from typing import Dict, Any, Optional, List
import boto3
from botocore.exceptions import ClientError
from loguru import logger

from config.settings import settings


class BedrockClient:
    """
    Client for interacting with AWS Bedrock Mistral 7B Instruct model
    """
    
    def __init__(self, region_name="us-east-1"):
        self.region_name = region_name
        self.model_id = "mistral.mistral-7b-instruct-v0:2"
        self.bedrock_runtime = boto3.client(
            service_name='bedrock-runtime',
            region_name=region_name
        )
        self.max_tokens = getattr(settings, 'bedrock_max_tokens', 2000)
        self.temperature = getattr(settings, 'bedrock_temperature', 0.1)
    
    async def analyze_verification(
        self,
        prompt: str,
        verification_type: str = "general"
    ) -> Dict[str, Any]:
        """
        Send verification prompt to Mistral 7B and get structured analysis
        """
        try:
            logger.info(f"Sending {verification_type} verification prompt to Bedrock Mistral 7B")
            
            # Prepare the prompt with system context
            system_prompt = self._get_system_prompt(verification_type)
            full_prompt = f"{system_prompt}\n\nHuman: {prompt}\n\nAssistant:"
            
            # Prepare request body for Mistral
            request_body = {
                "prompt": full_prompt,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "top_p": 0.9,
                "top_k": 50,
                "stop": ["Human:", "\n\nHuman:"]
            }
            
            # Invoke the model
            response = await self._invoke_model_async(request_body)
            
            # Parse the response
            response_body = json.loads(response.get('body').read())
            generated_text = response_body.get('outputs', [{}])[0].get('text', '')
            
            # Extract JSON from the response
            analysis = self._extract_json_from_response(generated_text)
            
            logger.info(f"Bedrock analysis completed for {verification_type}")
            return analysis
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Bedrock response as JSON: {str(e)}")
            return self._get_fallback_response(verification_type)
            
        except ClientError as e:
            logger.error(f"AWS Bedrock client error: {str(e)}")
            return self._get_fallback_response(verification_type)
            
        except Exception as e:
            logger.error(f"Bedrock analysis failed: {str(e)}")
            return self._get_fallback_response(verification_type)
    
    async def _invoke_model_async(self, request_body: Dict[str, Any]) -> Dict[str, Any]:
        """
        Invoke Bedrock model asynchronously
        """
        loop = asyncio.get_event_loop()
        
        def invoke_model():
            return self.bedrock_runtime.invoke_model(
                body=json.dumps(request_body),
                modelId=self.model_id,
                accept='application/json',
                contentType='application/json'
            )
        
        return await loop.run_in_executor(None, invoke_model)
    
    def _extract_json_from_response(self, text: str) -> Dict[str, Any]:
        """
        Extract JSON from Mistral response text
        """
        # Look for JSON in the response
        text = text.strip()
        
        # Find JSON block
        start_idx = text.find('{')
        if start_idx == -1:
            raise json.JSONDecodeError("No JSON found in response", text, 0)
        
        # Find the matching closing brace
        brace_count = 0
        end_idx = start_idx
        
        for i, char in enumerate(text[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i + 1
                    break
        
        json_str = text[start_idx:end_idx]
        return json.loads(json_str)
    
    async def analyze_employment_verification(
        self,
        prompt: str
    ) -> Dict[str, Any]:
        """Analyze employment verification with specialized handling"""
        return await self.analyze_verification(prompt, "employment")
    
    async def analyze_education_verification(
        self,
        prompt: str
    ) -> Dict[str, Any]:
        """Analyze education verification with specialized handling"""
        return await self.analyze_verification(prompt, "education")
    
    async def analyze_personal_info_verification(
        self,
        prompt: str
    ) -> Dict[str, Any]:
        """Analyze personal information verification with specialized handling"""
        return await self.analyze_verification(prompt, "personal_info")
    
    async def analyze_comprehensive_verification(
        self,
        prompt: str
    ) -> Dict[str, Any]:
        """Analyze comprehensive verification with specialized handling"""
        return await self.analyze_verification(prompt, "comprehensive")
    
    def _get_system_prompt(self, verification_type: str) -> str:
        """Get system prompt based on verification type"""
        
        base_prompt = """You are an expert background verification analyst with extensive experience in candidate screening and risk assessment. You have deep knowledge of employment verification, education validation, and identity confirmation processes.

Your analysis should be:
- Thorough and evidence-based
- Objective and unbiased
- Focused on risk assessment
- Compliant with verification standards
- Clear and actionable

Always provide confidence scores between 0.0 and 1.0, where:
- 1.0 = Perfect match, no concerns
- 0.8-0.9 = Strong match, minor discrepancies
- 0.6-0.7 = Moderate match, some concerns
- 0.4-0.5 = Weak match, significant concerns
- 0.0-0.3 = Poor match, major red flags

Color codes should be:
- Green: High confidence (0.9+), minimal risk
- Amber: Medium confidence (0.7-0.89), moderate risk
- Red: Low confidence (<0.7), high risk

You must respond with valid JSON only. Do not include any explanatory text outside the JSON structure."""

        type_specific = {
            "employment": """
Focus specifically on employment verification:
- Employer name accuracy and legitimacy
- Employment date consistency
- Salary verification and reasonableness
- Job title and responsibility alignment
- Location and workplace verification
- Employment gap analysis
""",
            "education": """
Focus specifically on education verification:
- Institution name accuracy and accreditation
- Degree verification and legitimacy
- Field of study alignment
- Graduation date consistency
- Grade and academic performance
- Credential authenticity
""",
            "personal_info": """
Focus specifically on personal information verification:
- Name consistency and variations
- Identity document verification
- Contact information accuracy
- Address verification
- Government verification status
- Identity fraud indicators
""",
            "comprehensive": """
Focus on overall verification assessment:
- Pattern analysis across all data points
- Risk level assessment
- Hiring recommendation
- Additional verification needs
- Compliance considerations
- Decision support insights
"""
        }
        
        return base_prompt + "\n" + type_specific.get(verification_type, "")
    
    def _get_fallback_response(self, verification_type: str) -> Dict[str, Any]:
        """Get fallback response when Bedrock analysis fails"""
        
        base_response = {
            "overall_assessment": {
                "confidence_score": 0.5,
                "color_code": "Amber",
                "summary": "Analysis could not be completed due to technical issues. Manual review required."
            },
            "requires_human_review": True,
            "error": "Bedrock analysis failed"
        }
        
        if verification_type == "employment":
            base_response.update({
                "employer_match": {
                    "score": 0.5,
                    "explanation": "Could not analyze employer match"
                },
                "date_verification": {
                    "start_date_score": 0.5,
                    "end_date_score": 0.5,
                    "explanation": "Could not analyze date verification"
                },
                "salary_verification": {
                    "score": 0.5,
                    "explanation": "Could not analyze salary verification"
                }
            })
        
        elif verification_type == "education":
            base_response.update({
                "institution_match": {
                    "score": 0.5,
                    "explanation": "Could not analyze institution match"
                },
                "degree_verification": {
                    "score": 0.5,
                    "explanation": "Could not analyze degree verification"
                }
            })
        
        elif verification_type == "personal_info":
            base_response.update({
                "name_verification": {
                    "score": 0.5,
                    "explanation": "Could not analyze name verification"
                },
                "id_verification": {
                    "score": 0.5,
                    "explanation": "Could not analyze ID verification"
                },
                "contact_verification": {
                    "score": 0.5,
                    "explanation": "Could not analyze contact verification"
                }
            })
        
        elif verification_type == "comprehensive":
            base_response.update({
                "key_findings": {
                    "strengths": [],
                    "concerns": ["Technical analysis failure"],
                    "critical_issues": ["Manual review required"]
                },
                "recommendations": {
                    "hiring_decision": "Conditional",
                    "additional_verification": ["Manual review of all data"],
                    "human_review_required": True
                }
            })
        
        return base_response
    
    async def batch_analyze(
        self,
        prompts: List[Dict[str, str]],
        max_concurrent: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Analyze multiple verification prompts concurrently
        
        Args:
            prompts: List of dicts with 'prompt' and 'type' keys
            max_concurrent: Maximum concurrent requests
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def analyze_single(prompt_data):
            async with semaphore:
                return await self.analyze_verification(
                    prompt_data['prompt'],
                    prompt_data.get('type', 'general')
                )
        
        tasks = [analyze_single(prompt_data) for prompt_data in prompts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions in results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch analysis failed for prompt {i}: {str(result)}")
                processed_results.append(
                    self._get_fallback_response(prompts[i].get('type', 'general'))
                )
            else:
                processed_results.append(result)
        
        return processed_results
    
    def validate_response_format(
        self,
        response: Dict[str, Any],
        verification_type: str
    ) -> bool:
        """Validate that Bedrock response has expected format"""
        
        required_fields = ["overall_assessment"]
        
        if verification_type == "employment":
            required_fields.extend(["employer_match", "date_verification", "salary_verification"])
        elif verification_type == "education":
            required_fields.extend(["institution_match", "degree_verification"])
        elif verification_type == "personal_info":
            required_fields.extend(["name_verification", "id_verification", "contact_verification"])
        elif verification_type == "comprehensive":
            required_fields.extend(["key_findings", "recommendations"])
        
        for field in required_fields:
            if field not in response:
                logger.warning(f"Missing required field in Bedrock response: {field}")
                return False
        
        # Validate overall assessment structure
        overall = response.get("overall_assessment", {})
        if not all(key in overall for key in ["confidence_score", "color_code"]):
            logger.warning("Invalid overall_assessment structure in Bedrock response")
            return False
        
        return True
