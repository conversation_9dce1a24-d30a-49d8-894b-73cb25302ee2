"""
DynamoDB Client for retrieving verification data
"""
import json
from typing import Dict, Any, Optional, <PERSON><PERSON>
import boto3
from botocore.exceptions import ClientError
from loguru import logger

from ..models import EnrichedCPF, ClientInput, ValidationMatrix


class DynamoDBClient:
    """
    Client for interacting with DynamoDB to retrieve verification data
    """
    
    def __init__(self, region_name="us-east-1", table_name="Resume-PII-Processor-table"):
        self.region_name = region_name
        self.table_name = table_name
        self.dynamodb = boto3.resource('dynamodb', region_name=region_name)
        self.table = self.dynamodb.Table(table_name)
    
    async def get_verification_data(
        self, 
        job_id: str
    ) -> <PERSON>ple[Optional[EnrichedCPF], Optional[ClientInput], Optional[ValidationMatrix]]:
        """
        Retrieve verification data from DynamoDB using job_id
        
        Args:
            job_id: The job identifier to retrieve data for
            
        Returns:
            Tuple of (enriched_cpf, client_input, validation_matrix)
        """
        try:
            logger.info(f"Retrieving verification data for job_id: {job_id}")
            
            # Get item from DynamoDB
            response = self.table.get_item(
                Key={'job_id': job_id}
            )
            
            if 'Item' not in response:
                logger.error(f"No data found for job_id: {job_id}")
                return None, None, None
            
            item = response['Item']
            logger.info(f"Retrieved data for job_id: {job_id}")
            
            # Parse the data fields
            enriched_cpf = self._parse_enriched_cpf(item.get('cpf'))
            client_input = self._parse_client_input(item.get('client_input'))
            validation_matrix = self._parse_validation_matrix(item.get('validation_matrix'))
            
            return enriched_cpf, client_input, validation_matrix
            
        except ClientError as e:
            logger.error(f"DynamoDB client error retrieving job_id {job_id}: {str(e)}")
            return None, None, None
        except Exception as e:
            logger.error(f"Error retrieving verification data for job_id {job_id}: {str(e)}")
            return None, None, None
    
    def _parse_enriched_cpf(self, cpf_data: Any) -> Optional[EnrichedCPF]:
        """
        Parse enriched CPF data from DynamoDB item
        """
        if not cpf_data:
            logger.warning("No CPF data found in DynamoDB item")
            return None
        
        try:
            # Handle different data formats
            if isinstance(cpf_data, str):
                cpf_dict = json.loads(cpf_data)
            elif isinstance(cpf_data, dict):
                cpf_dict = cpf_data
            else:
                logger.error(f"Unexpected CPF data format: {type(cpf_data)}")
                return None
            
            # Create EnrichedCPF object
            enriched_cpf = EnrichedCPF(**cpf_dict)
            logger.debug("Successfully parsed EnrichedCPF")
            return enriched_cpf
            
        except Exception as e:
            logger.error(f"Error parsing EnrichedCPF: {str(e)}")
            return None
    
    def _parse_client_input(self, client_input_data: Any) -> Optional[ClientInput]:
        """
        Parse client input data from DynamoDB item
        """
        if not client_input_data:
            logger.warning("No client input data found in DynamoDB item")
            return None
        
        try:
            # Handle different data formats
            if isinstance(client_input_data, str):
                client_dict = json.loads(client_input_data)
            elif isinstance(client_input_data, dict):
                client_dict = client_input_data
            else:
                logger.error(f"Unexpected client input data format: {type(client_input_data)}")
                return None
            
            # Create ClientInput object
            client_input = ClientInput(**client_dict)
            logger.debug("Successfully parsed ClientInput")
            return client_input
            
        except Exception as e:
            logger.error(f"Error parsing ClientInput: {str(e)}")
            return None
    
    def _parse_validation_matrix(self, validation_matrix_data: Any) -> Optional[ValidationMatrix]:
        """
        Parse validation matrix data from DynamoDB item
        """
        if not validation_matrix_data:
            logger.warning("No validation matrix data found in DynamoDB item")
            return None
        
        try:
            # Handle different data formats
            if isinstance(validation_matrix_data, str):
                matrix_dict = json.loads(validation_matrix_data)
            elif isinstance(validation_matrix_data, dict):
                matrix_dict = validation_matrix_data
            else:
                logger.error(f"Unexpected validation matrix data format: {type(validation_matrix_data)}")
                return None
            
            # Create ValidationMatrix object
            validation_matrix = ValidationMatrix(**matrix_dict)
            logger.debug("Successfully parsed ValidationMatrix")
            return validation_matrix
            
        except Exception as e:
            logger.error(f"Error parsing ValidationMatrix: {str(e)}")
            return None
    
    async def update_job_status(
        self, 
        job_id: str, 
        status: str, 
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """
        Update job status in DynamoDB
        
        Args:
            job_id: The job identifier
            status: New status (pending, in_progress, completed, failed)
            result: Verification result data (for completed jobs)
            error_message: Error message (for failed jobs)
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            update_expression = "SET job_status = :status, updated_at = :timestamp"
            expression_values = {
                ':status': status,
                ':timestamp': self._get_current_timestamp()
            }
            
            if result:
                update_expression += ", verification_result = :result"
                expression_values[':result'] = json.dumps(result, default=str)
            
            if error_message:
                update_expression += ", error_message = :error"
                expression_values[':error'] = error_message
            
            self.table.update_item(
                Key={'job_id': job_id},
                UpdateExpression=update_expression,
                ExpressionAttributeValues=expression_values
            )
            
            logger.info(f"Updated job status for {job_id} to {status}")
            return True
            
        except ClientError as e:
            logger.error(f"DynamoDB client error updating job {job_id}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error updating job status for {job_id}: {str(e)}")
            return False
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.utcnow().isoformat()
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current job status from DynamoDB
        
        Args:
            job_id: The job identifier
            
        Returns:
            Job status information or None if not found
        """
        try:
            response = self.table.get_item(
                Key={'job_id': job_id},
                ProjectionExpression='job_id, job_status, created_at, updated_at, error_message'
            )
            
            if 'Item' not in response:
                logger.warning(f"Job {job_id} not found in DynamoDB")
                return None
            
            return dict(response['Item'])
            
        except ClientError as e:
            logger.error(f"DynamoDB client error getting job status {job_id}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {str(e)}")
            return None
    
    async def store_verification_result(
        self, 
        job_id: str, 
        result: Dict[str, Any]
    ) -> bool:
        """
        Store verification result in DynamoDB
        
        Args:
            job_id: The job identifier
            result: Verification result data
            
        Returns:
            True if storage successful, False otherwise
        """
        try:
            self.table.update_item(
                Key={'job_id': job_id},
                UpdateExpression="SET verification_result = :result, completed_at = :timestamp",
                ExpressionAttributeValues={
                    ':result': json.dumps(result, default=str),
                    ':timestamp': self._get_current_timestamp()
                }
            )
            
            logger.info(f"Stored verification result for job {job_id}")
            return True
            
        except ClientError as e:
            logger.error(f"DynamoDB client error storing result for {job_id}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error storing verification result for {job_id}: {str(e)}")
            return False
    
    def create_job_record(
        self,
        job_id: str,
        cpf_data: Dict[str, Any],
        client_input_data: Dict[str, Any],
        validation_matrix_data: Dict[str, Any]
    ) -> bool:
        """
        Create initial job record in DynamoDB
        
        Args:
            job_id: The job identifier
            cpf_data: Enriched CPF data
            client_input_data: Client input data
            validation_matrix_data: Validation matrix data
            
        Returns:
            True if creation successful, False otherwise
        """
        try:
            item = {
                'job_id': job_id,
                'cpf': json.dumps(cpf_data, default=str),
                'client_input': json.dumps(client_input_data, default=str),
                'validation_matrix': json.dumps(validation_matrix_data, default=str),
                'job_status': 'pending',
                'created_at': self._get_current_timestamp()
            }
            
            self.table.put_item(Item=item)
            logger.info(f"Created job record for {job_id}")
            return True
            
        except ClientError as e:
            logger.error(f"DynamoDB client error creating job {job_id}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error creating job record for {job_id}: {str(e)}")
            return False
    
    def batch_get_jobs(self, job_ids: list) -> Dict[str, Dict[str, Any]]:
        """
        Retrieve multiple jobs in batch
        
        Args:
            job_ids: List of job identifiers
            
        Returns:
            Dictionary mapping job_id to job data
        """
        try:
            if not job_ids:
                return {}
            
            # DynamoDB batch_get_item has a limit of 100 items
            batch_size = 100
            all_items = {}
            
            for i in range(0, len(job_ids), batch_size):
                batch_job_ids = job_ids[i:i + batch_size]
                
                request_items = {
                    self.table_name: {
                        'Keys': [{'job_id': job_id} for job_id in batch_job_ids]
                    }
                }
                
                response = self.dynamodb.batch_get_item(RequestItems=request_items)
                
                for item in response.get('Responses', {}).get(self.table_name, []):
                    all_items[item['job_id']] = dict(item)
            
            logger.info(f"Retrieved {len(all_items)} jobs in batch")
            return all_items
            
        except ClientError as e:
            logger.error(f"DynamoDB client error in batch get: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Error in batch get jobs: {str(e)}")
            return {}
