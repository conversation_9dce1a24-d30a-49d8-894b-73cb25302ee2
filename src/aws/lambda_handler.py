"""
AWS Lambda handler for background verification processing
"""
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger
import sys

# Configure logger for Lambda
logger.remove()
logger.add(sys.stdout, level="INFO", format="{time} | {level} | {message}")

from ..verification import VerificationEngine
from ..scoring import VerificationScorer
from ..prompt_engineering.bedrock_client import BedrockClient
from .dynamodb_client import DynamoDBClient
from .sqs_client import SQSClient


class LambdaVerificationProcessor:
    """
    Main processor for Lambda-based verification
    """
    
    def __init__(self):
        self.verification_engine = VerificationEngine()
        self.scorer = VerificationScorer()
        self.bedrock_client = BedrockClient()
        self.dynamodb_client = DynamoDBClient()
        self.sqs_client = SQSClient()
        
        # Replace the LLM client in verification engine
        self.verification_engine.prompt_manager.llm_client = self.bedrock_client
    
    async def process_verification_job(self, job_id: str) -> Dict[str, Any]:
        """
        Process a single verification job
        
        Args:
            job_id: The job identifier from SQS message
            
        Returns:
            Processing result dictionary
        """
        try:
            logger.info(f"Starting verification processing for job_id: {job_id}")
            
            # Update job status to in_progress
            await self.dynamodb_client.update_job_status(job_id, "in_progress")
            
            # Retrieve verification data from DynamoDB
            enriched_cpf, client_input, validation_matrix = await self.dynamodb_client.get_verification_data(job_id)
            
            if not all([enriched_cpf, client_input, validation_matrix]):
                error_msg = f"Missing verification data for job_id: {job_id}"
                logger.error(error_msg)
                await self.dynamodb_client.update_job_status(job_id, "failed", error_message=error_msg)
                return {
                    "status": "failed",
                    "error": error_msg,
                    "job_id": job_id
                }
            
            logger.info(f"Retrieved verification data for candidate: {enriched_cpf.candidate_id}")
            
            # Perform verification
            verification_result = await self.verification_engine.verify_candidate(
                enriched_cpf,
                client_input,
                validation_matrix
            )
            
            # Generate comprehensive summary
            summary = self.scorer.generate_verification_summary(verification_result, validation_matrix)
            
            # Prepare result data
            result_data = {
                "job_id": job_id,
                "candidate_id": verification_result.candidate_id,
                "status": verification_result.status.value,
                "overall_score": verification_result.overall_score,
                "overall_color_code": verification_result.overall_color_code.value,
                "requires_human_review": verification_result.requires_human_review,
                "summary": verification_result.summary,
                "field_results": [
                    {
                        "field_name": r.field_name,
                        "color_code": r.color_code.value,
                        "confidence_score": r.confidence_score,
                        "remark": r.remark,
                        "expected_value": r.expected_value,
                        "actual_value": r.actual_value,
                        "variance": r.variance,
                        "requires_human_review": r.requires_human_review
                    }
                    for r in verification_result.field_results
                ],
                "processing_summary": summary,
                "processing_time_seconds": verification_result.processing_time_seconds,
                "completed_at": datetime.utcnow().isoformat()
            }
            
            # Store result in DynamoDB
            await self.dynamodb_client.store_verification_result(job_id, result_data)
            
            # Update job status to completed
            await self.dynamodb_client.update_job_status(job_id, "completed", result=result_data)
            
            logger.info(f"Verification completed successfully for job_id: {job_id}")
            
            return {
                "status": "completed",
                "job_id": job_id,
                "result": result_data
            }
            
        except Exception as e:
            error_msg = f"Verification processing failed for job_id {job_id}: {str(e)}"
            logger.error(error_msg)
            
            # Update job status to failed
            await self.dynamodb_client.update_job_status(job_id, "failed", error_message=error_msg)
            
            return {
                "status": "failed",
                "error": error_msg,
                "job_id": job_id
            }


# Global processor instance
def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler function
    
    Args:
        event: Lambda event containing SQS messages
        context: Lambda context object
        
    Returns:
        Processing results
    """
    try:
        logger.info(f"Lambda invoked with event: {json.dumps(event, default=str)}")
        
        # Handle SQS event
        if 'Records' in event:
            return handle_sqs_event(event, context)
        
        # Handle direct invocation with job_id
        elif 'job_id' in event:
            return handle_direct_invocation(event, context)
        
        else:
            logger.error("Invalid event format - no Records or job_id found")
            return {
                "statusCode": 400,
                "body": json.dumps({
                    "error": "Invalid event format",
                    "message": "Event must contain either 'Records' (SQS) or 'job_id' (direct)"
                })
            }
            
    except Exception as e:
        logger.error(f"Lambda handler error: {str(e)}")
        return {
            "statusCode": 500,
            "body": json.dumps({
                "error": "Internal server error",
                "message": str(e)
            })
        }


def handle_sqs_event(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle SQS event with multiple records
    """
    results = []
    
    for record in event['Records']:
        try:
            # Extract job_id from SQS message
            message_body = json.loads(record['body'])
            job_id = message_body.get('job_id')
            
            if not job_id:
                logger.error(f"No job_id found in SQS message: {record['body']}")
                results.append({
                    "status": "failed",
                    "error": "No job_id in message",
                    "messageId": record.get('messageId')
                })
                continue
            
            logger.info(f"Processing SQS message for job_id: {job_id}")
            
            # Process the verification job
            result = asyncio.run(processor.process_verification_job(job_id))
            results.append(result)
            
        except Exception as e:
            logger.error(f"Error processing SQS record: {str(e)}")
            results.append({
                "status": "failed",
                "error": str(e),
                "messageId": record.get('messageId')
            })
    
    # Return batch results
    return {
        "statusCode": 200,
        "body": json.dumps({
            "processed_count": len(results),
            "results": results
        })
    }


def handle_direct_invocation(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle direct Lambda invocation with job_id
    """
    job_id = event['job_id']
    logger.info(f"Processing direct invocation for job_id: {job_id}")
    
    # Process the verification job
    result = asyncio.run(processor.process_verification_job(job_id))
    
    return {
        "statusCode": 200,
        "body": json.dumps(result)
    }


def health_check_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Health check handler for Lambda function
    """
    try:
        # Basic health checks
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "lambda_function": context.function_name if context else "unknown",
            "lambda_version": context.function_version if context else "unknown",
            "services": {
                "verification_engine": "operational",
                "bedrock_client": "operational",
                "dynamodb_client": "operational",
                "sqs_client": "operational"
            }
        }
        
        # Test DynamoDB connectivity
        try:
            dynamodb_client = DynamoDBClient()
            # Simple connectivity test - this will fail gracefully if DynamoDB is not accessible
            health_status["services"]["dynamodb"] = "operational"
        except Exception as e:
            logger.warning(f"DynamoDB health check failed: {str(e)}")
            health_status["services"]["dynamodb"] = "degraded"
        
        # Test Bedrock connectivity
        try:
            bedrock_client = BedrockClient()
            health_status["services"]["bedrock"] = "operational"
        except Exception as e:
            logger.warning(f"Bedrock health check failed: {str(e)}")
            health_status["services"]["bedrock"] = "degraded"
        
        return {
            "statusCode": 200,
            "body": json.dumps(health_status)
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "statusCode": 500,
            "body": json.dumps({
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            })
        }


# Additional handler for batch processing
def batch_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handler for batch processing multiple jobs
    """
    try:
        job_ids = event.get('job_ids', [])
        
        if not job_ids:
            return {
                "statusCode": 400,
                "body": json.dumps({
                    "error": "No job_ids provided",
                    "message": "Event must contain 'job_ids' array"
                })
            }
        
        logger.info(f"Processing batch of {len(job_ids)} jobs")
        
        # Process jobs concurrently (with limit to avoid overwhelming resources)
        async def process_batch():
            semaphore = asyncio.Semaphore(5)  # Limit concurrent processing
            
            async def process_single_job(job_id):
                async with semaphore:
                    return await processor.process_verification_job(job_id)
            
            tasks = [process_single_job(job_id) for job_id in job_ids]
            return await asyncio.gather(*tasks, return_exceptions=True)
        
        results = asyncio.run(process_batch())
        
        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "status": "failed",
                    "error": str(result),
                    "job_id": job_ids[i]
                })
            else:
                processed_results.append(result)
        
        return {
            "statusCode": 200,
            "body": json.dumps({
                "processed_count": len(processed_results),
                "results": processed_results
            })
        }
        
    except Exception as e:
        logger.error(f"Batch processing error: {str(e)}")
        return {
            "statusCode": 500,
            "body": json.dumps({
                "error": "Batch processing failed",
                "message": str(e)
            })
        }
