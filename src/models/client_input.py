"""
Client Input model
Contains expected values for candidate verification
"""
from datetime import date
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator

from .base import BaseVerificationModel, Address


class ExpectedEmployment(BaseModel):
    """Expected employment information from client"""
    
    employer_name: str = Field(..., description="Expected employer name")
    job_title: Optional[str] = Field(None, description="Expected job title")
    start_date: Optional[date] = Field(None, description="Expected start date")
    end_date: Optional[date] = Field(None, description="Expected end date")
    
    # Salary expectations
    min_salary: Optional[float] = Field(None, description="Minimum expected salary")
    max_salary: Optional[float] = Field(None, description="Maximum expected salary")
    currency: Optional[str] = Field("USD", description="Salary currency")
    
    # Location expectations
    expected_location: Optional[Address] = Field(None, description="Expected work location")
    
    # Employment type
    employment_type: Optional[str] = Field(None, description="Expected employment type")
    
    # Priority level for this employment verification
    priority: Optional[str] = Field("medium", description="Priority level: low, medium, high, critical")
    
    # Additional requirements
    must_verify: bool = Field(default=True, description="Whether this employment must be verified")
    
    @validator('max_salary')
    def validate_salary_range(cls, v, values):
        if v is not None and 'min_salary' in values and values['min_salary'] is not None:
            if v < values['min_salary']:
                raise ValueError('Maximum salary cannot be less than minimum salary')
        return v


class ExpectedEducation(BaseModel):
    """Expected education information from client"""
    
    institution_name: str = Field(..., description="Expected institution name")
    degree: str = Field(..., description="Expected degree")
    field_of_study: Optional[str] = Field(None, description="Expected field of study")
    graduation_date: Optional[date] = Field(None, description="Expected graduation date")
    
    # Grade expectations
    min_grade: Optional[str] = Field(None, description="Minimum expected grade")
    
    # Location expectations
    expected_location: Optional[Address] = Field(None, description="Expected institution location")
    
    # Priority level
    priority: Optional[str] = Field("medium", description="Priority level: low, medium, high, critical")
    
    # Verification requirements
    must_verify: bool = Field(default=True, description="Whether this education must be verified")


class ExpectedPersonalInfo(BaseModel):
    """Expected personal information from client"""
    
    full_name: str = Field(..., description="Expected full name")
    alternative_names: Optional[List[str]] = Field(
        default_factory=list, 
        description="Alternative names or aliases to check"
    )
    
    date_of_birth: Optional[date] = Field(None, description="Expected date of birth")
    national_id: Optional[str] = Field(None, description="Expected national ID")
    passport_number: Optional[str] = Field(None, description="Expected passport number")
    
    # Contact information
    email: Optional[str] = Field(None, description="Expected email address")
    phone: Optional[str] = Field(None, description="Expected phone number")
    
    # Address expectations
    current_address: Optional[Address] = Field(None, description="Expected current address")
    permanent_address: Optional[Address] = Field(None, description="Expected permanent address")


class VerificationRequirements(BaseModel):
    """Specific verification requirements from client"""
    
    # Required verification levels
    employment_verification_required: bool = Field(default=True)
    education_verification_required: bool = Field(default=True)
    identity_verification_required: bool = Field(default=True)
    address_verification_required: bool = Field(default=False)
    
    # Specific checks
    criminal_background_check: bool = Field(default=False)
    credit_check: bool = Field(default=False)
    reference_check: bool = Field(default=False)
    
    # Time constraints
    verification_deadline: Optional[date] = Field(None, description="Deadline for verification")
    priority_level: str = Field(default="medium", description="Overall priority: low, medium, high, critical")
    
    # Compliance requirements
    compliance_standards: Optional[List[str]] = Field(
        default_factory=list,
        description="Required compliance standards (e.g., 'GDPR', 'SOX', 'HIPAA')"
    )
    
    # Custom verification rules
    custom_rules: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Custom verification rules specific to this client"
    )


class ClientInput(BaseVerificationModel):
    """
    Client Input containing expected values and requirements for candidate verification
    """
    
    client_id: str = Field(..., description="Unique client identifier")
    candidate_id: str = Field(..., description="Candidate identifier for this verification")
    job_position: Optional[str] = Field(None, description="Position being hired for")
    
    # Expected information
    expected_personal_info: ExpectedPersonalInfo = Field(..., description="Expected personal information")
    expected_employment: List[ExpectedEmployment] = Field(
        default_factory=list,
        description="Expected employment history"
    )
    expected_education: List[ExpectedEducation] = Field(
        default_factory=list,
        description="Expected education history"
    )
    
    # Verification requirements
    verification_requirements: VerificationRequirements = Field(
        default_factory=VerificationRequirements,
        description="Verification requirements"
    )
    
    # Additional context
    hiring_context: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional context about the hiring process"
    )
    
    # Special instructions
    special_instructions: Optional[List[str]] = Field(
        default_factory=list,
        description="Special instructions for verification"
    )
    
    # Budget constraints
    verification_budget: Optional[float] = Field(None, description="Budget for verification process")
    
    def get_critical_verifications(self) -> List[str]:
        """Get list of critical verification items"""
        critical_items = []
        
        # Check employment
        for emp in self.expected_employment:
            if emp.priority == "critical":
                critical_items.append(f"Employment: {emp.employer_name}")
        
        # Check education
        for edu in self.expected_education:
            if edu.priority == "critical":
                critical_items.append(f"Education: {edu.institution_name}")
        
        return critical_items
    
    def get_verification_scope(self) -> Dict[str, bool]:
        """Get the scope of verification required"""
        return {
            'employment': self.verification_requirements.employment_verification_required,
            'education': self.verification_requirements.education_verification_required,
            'identity': self.verification_requirements.identity_verification_required,
            'address': self.verification_requirements.address_verification_required,
            'criminal_background': self.verification_requirements.criminal_background_check,
            'credit': self.verification_requirements.credit_check,
            'references': self.verification_requirements.reference_check,
        }
    
    def is_high_priority(self) -> bool:
        """Check if this is a high priority verification"""
        return self.verification_requirements.priority_level in ["high", "critical"]
    
    def get_expected_employer_names(self) -> List[str]:
        """Get list of expected employer names"""
        return [emp.employer_name for emp in self.expected_employment]
    
    def get_expected_institution_names(self) -> List[str]:
        """Get list of expected institution names"""
        return [edu.institution_name for edu in self.expected_education]
    
    def validate_requirements(self) -> List[str]:
        """Validate client input requirements and return any issues"""
        issues = []
        
        # Check if at least one verification type is required
        scope = self.get_verification_scope()
        if not any(scope.values()):
            issues.append("At least one verification type must be required")
        
        # Check for conflicting requirements
        if self.verification_requirements.verification_deadline:
            if self.verification_requirements.verification_deadline < date.today():
                issues.append("Verification deadline cannot be in the past")
        
        # Validate employment expectations
        for i, emp in enumerate(self.expected_employment):
            if emp.start_date and emp.end_date and emp.start_date > emp.end_date:
                issues.append(f"Employment {i+1}: Start date cannot be after end date")
        
        return issues
