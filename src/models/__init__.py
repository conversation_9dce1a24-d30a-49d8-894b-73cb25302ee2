"""
Data models for the ML Background Verification System
"""

# Base models
from .base import (
    ColorCode,
    VerificationStatus,
    BaseVerificationModel,
    PersonalInfo,
    Address,
    EmploymentRecord,
    EducationRecord,
    VerificationResult,
    OverallVerificationResult
)

# Enriched CPF models
from .enriched_cpf import (
    GovernmentVerification,
    CandidateDeclaredInfo,
    EnrichedCPF
)

# Client Input models
from .client_input import (
    ExpectedEmployment,
    ExpectedEducation,
    ExpectedPersonalInfo,
    VerificationRequirements,
    ClientInput
)

# Validation Matrix models
from .validation_matrix import (
    MatchType,
    FieldType,
    ValidationRule,
    EmploymentValidationRules,
    EducationValidationRules,
    PersonalInfoValidationRules,
    ComplianceRules,
    ValidationMatrix
)

__all__ = [
    # Base models
    "ColorCode",
    "VerificationStatus",
    "BaseVerificationModel",
    "PersonalInfo",
    "Address",
    "EmploymentRecord",
    "EducationRecord",
    "VerificationResult",
    "OverallVerificationResult",

    # Enriched CPF models
    "GovernmentVerification",
    "CandidateDeclaredInfo",
    "EnrichedCPF",

    # Client Input models
    "ExpectedEmployment",
    "ExpectedEducation",
    "ExpectedPersonalInfo",
    "VerificationRequirements",
    "ClientInput",

    # Validation Matrix models
    "MatchType",
    "FieldType",
    "ValidationRule",
    "EmploymentValidationRules",
    "EducationValidationRules",
    "PersonalInfoValidationRules",
    "ComplianceRules",
    "ValidationMatrix"
]