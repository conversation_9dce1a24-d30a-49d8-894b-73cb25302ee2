"""
Validation Matrix model
Contains company-specific comparison rules and validation logic
"""
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator

from .base import BaseVerificationModel, ColorCode


class MatchType(str, Enum):
    """Types of matching algorithms"""
    EXACT = "exact"
    FUZZY = "fuzzy"
    SEMANTIC = "semantic"
    REGEX = "regex"
    RANGE = "range"
    DATE_TOLERANCE = "date_tolerance"


class FieldType(str, Enum):
    """Types of fields for validation"""
    TEXT = "text"
    DATE = "date"
    NUMBER = "number"
    EMAIL = "email"
    PHONE = "phone"
    ADDRESS = "address"
    CURRENCY = "currency"


class ValidationRule(BaseModel):
    """Individual validation rule for a specific field"""
    
    field_name: str = Field(..., description="Name of the field to validate")
    field_type: FieldType = Field(..., description="Type of the field")
    match_type: MatchType = Field(..., description="Type of matching to perform")
    
    # Matching parameters
    exact_match_required: bool = Field(default=False, description="Whether exact match is required")
    similarity_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="Similarity threshold for fuzzy matching")
    
    # Tolerance settings
    date_tolerance_days: Optional[int] = Field(None, description="Tolerance in days for date fields")
    numeric_tolerance_percent: Optional[float] = Field(None, description="Tolerance percentage for numeric fields")
    numeric_tolerance_absolute: Optional[float] = Field(None, description="Absolute tolerance for numeric fields")
    
    # Priority and weight
    priority: str = Field(default="medium", description="Priority level: low, medium, high, critical")
    weight: float = Field(default=1.0, ge=0.0, description="Weight for overall scoring")
    
    # Required field
    is_required: bool = Field(default=True, description="Whether this field is required for verification")
    
    # Custom validation logic
    custom_validation_logic: Optional[str] = Field(None, description="Custom validation logic (Python expression)")
    
    # Acceptable values or patterns
    acceptable_values: Optional[List[str]] = Field(None, description="List of acceptable values")
    regex_pattern: Optional[str] = Field(None, description="Regex pattern for validation")
    
    # Scoring rules
    green_threshold: float = Field(default=0.9, ge=0.0, le=1.0, description="Threshold for green classification")
    amber_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Threshold for amber classification")
    
    @validator('amber_threshold')
    def validate_thresholds(cls, v, values):
        if 'green_threshold' in values and v >= values['green_threshold']:
            raise ValueError('Amber threshold must be less than green threshold')
        return v


class EmploymentValidationRules(BaseModel):
    """Validation rules specific to employment verification"""
    
    employer_name_rule: ValidationRule = Field(..., description="Rule for employer name validation")
    job_title_rule: Optional[ValidationRule] = Field(None, description="Rule for job title validation")
    start_date_rule: ValidationRule = Field(..., description="Rule for start date validation")
    end_date_rule: Optional[ValidationRule] = Field(None, description="Rule for end date validation")
    salary_rule: Optional[ValidationRule] = Field(None, description="Rule for salary validation")
    location_rule: Optional[ValidationRule] = Field(None, description="Rule for location validation")
    
    # Employment-specific settings
    allow_employment_gaps: bool = Field(default=True, description="Whether employment gaps are allowed")
    max_gap_days: Optional[int] = Field(None, description="Maximum allowed gap in days")
    require_current_employment: bool = Field(default=False, description="Whether current employment is required")
    
    # Overlapping employment handling
    allow_overlapping_employment: bool = Field(default=False, description="Whether overlapping employment is allowed")
    max_overlap_days: Optional[int] = Field(None, description="Maximum allowed overlap in days")


class EducationValidationRules(BaseModel):
    """Validation rules specific to education verification"""
    
    institution_name_rule: ValidationRule = Field(..., description="Rule for institution name validation")
    degree_rule: ValidationRule = Field(..., description="Rule for degree validation")
    field_of_study_rule: Optional[ValidationRule] = Field(None, description="Rule for field of study validation")
    graduation_date_rule: Optional[ValidationRule] = Field(None, description="Rule for graduation date validation")
    grade_rule: Optional[ValidationRule] = Field(None, description="Rule for grade validation")
    
    # Education-specific settings
    require_degree_verification: bool = Field(default=True, description="Whether degree verification is required")
    accept_equivalent_degrees: bool = Field(default=True, description="Whether equivalent degrees are acceptable")
    
    # Grade requirements
    minimum_grade_required: Optional[str] = Field(None, description="Minimum grade required")


class PersonalInfoValidationRules(BaseModel):
    """Validation rules for personal information"""
    
    name_rule: ValidationRule = Field(..., description="Rule for name validation")
    date_of_birth_rule: Optional[ValidationRule] = Field(None, description="Rule for date of birth validation")
    national_id_rule: Optional[ValidationRule] = Field(None, description="Rule for national ID validation")
    passport_rule: Optional[ValidationRule] = Field(None, description="Rule for passport validation")
    email_rule: Optional[ValidationRule] = Field(None, description="Rule for email validation")
    phone_rule: Optional[ValidationRule] = Field(None, description="Rule for phone validation")
    address_rule: Optional[ValidationRule] = Field(None, description="Rule for address validation")
    
    # Name matching settings
    allow_name_variations: bool = Field(default=True, description="Whether name variations are allowed")
    check_alternative_names: bool = Field(default=True, description="Whether to check alternative names")


class ComplianceRules(BaseModel):
    """Compliance and regulatory rules"""
    
    data_retention_days: int = Field(default=2555, description="Data retention period in days (7 years default)")
    require_consent: bool = Field(default=True, description="Whether explicit consent is required")
    
    # Regulatory compliance
    gdpr_compliant: bool = Field(default=True, description="GDPR compliance required")
    hipaa_compliant: bool = Field(default=False, description="HIPAA compliance required")
    sox_compliant: bool = Field(default=False, description="SOX compliance required")
    
    # Audit requirements
    audit_trail_required: bool = Field(default=True, description="Whether audit trail is required")
    log_all_access: bool = Field(default=True, description="Whether to log all data access")
    
    # Data handling
    encrypt_sensitive_data: bool = Field(default=True, description="Whether to encrypt sensitive data")
    anonymize_results: bool = Field(default=False, description="Whether to anonymize results")


class ValidationMatrix(BaseVerificationModel):
    """
    Validation Matrix containing company-specific comparison rules and validation logic
    """
    
    company_id: str = Field(..., description="Unique company identifier")
    matrix_name: str = Field(..., description="Name of this validation matrix")
    version: str = Field(default="1.0", description="Version of the validation matrix")
    
    # Validation rules by category
    employment_rules: EmploymentValidationRules = Field(..., description="Employment validation rules")
    education_rules: EducationValidationRules = Field(..., description="Education validation rules")
    personal_info_rules: PersonalInfoValidationRules = Field(..., description="Personal info validation rules")
    
    # Overall scoring configuration
    overall_scoring_weights: Dict[str, float] = Field(
        default_factory=lambda: {
            "employment": 0.4,
            "education": 0.3,
            "personal_info": 0.3
        },
        description="Weights for overall scoring"
    )
    
    # Global thresholds
    global_green_threshold: float = Field(default=0.9, ge=0.0, le=1.0)
    global_amber_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    human_review_threshold: float = Field(default=0.6, ge=0.0, le=1.0)
    
    # Compliance rules
    compliance_rules: ComplianceRules = Field(default_factory=ComplianceRules)
    
    # Custom rules and exceptions
    custom_rules: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Custom validation rules specific to this company"
    )
    
    # Industry-specific settings
    industry_type: Optional[str] = Field(None, description="Industry type for specialized rules")
    regulatory_requirements: Optional[List[str]] = Field(
        default_factory=list,
        description="Specific regulatory requirements"
    )
    
    def get_field_rule(self, category: str, field_name: str) -> Optional[ValidationRule]:
        """Get validation rule for a specific field"""
        category_rules = {
            "employment": self.employment_rules,
            "education": self.education_rules,
            "personal_info": self.personal_info_rules
        }
        
        if category not in category_rules:
            return None
        
        rules_obj = category_rules[category]
        rule_attr = f"{field_name}_rule"
        
        return getattr(rules_obj, rule_attr, None)
    
    def get_color_code(self, score: float, field_rule: Optional[ValidationRule] = None) -> ColorCode:
        """Determine color code based on score and rules"""
        if field_rule:
            green_threshold = field_rule.green_threshold
            amber_threshold = field_rule.amber_threshold
        else:
            green_threshold = self.global_green_threshold
            amber_threshold = self.global_amber_threshold
        
        if score >= green_threshold:
            return ColorCode.GREEN
        elif score >= amber_threshold:
            return ColorCode.AMBER
        else:
            return ColorCode.RED
    
    def requires_human_review(self, score: float) -> bool:
        """Check if score requires human review"""
        return score < self.human_review_threshold
    
    def get_critical_fields(self) -> List[str]:
        """Get list of critical fields that must be verified"""
        critical_fields = []
        
        # Check employment rules
        for field_name in ["employer_name", "start_date", "end_date", "salary", "location"]:
            rule = getattr(self.employment_rules, f"{field_name}_rule", None)
            if rule and rule.priority == "critical":
                critical_fields.append(f"employment.{field_name}")
        
        # Check education rules
        for field_name in ["institution_name", "degree", "graduation_date"]:
            rule = getattr(self.education_rules, f"{field_name}_rule", None)
            if rule and rule.priority == "critical":
                critical_fields.append(f"education.{field_name}")
        
        # Check personal info rules
        for field_name in ["name", "date_of_birth", "national_id"]:
            rule = getattr(self.personal_info_rules, f"{field_name}_rule", None)
            if rule and rule.priority == "critical":
                critical_fields.append(f"personal_info.{field_name}")
        
        return critical_fields
    
    def validate_matrix(self) -> List[str]:
        """Validate the matrix configuration and return any issues"""
        issues = []
        
        # Check threshold consistency
        if self.global_amber_threshold >= self.global_green_threshold:
            issues.append("Global amber threshold must be less than green threshold")
        
        # Check weights sum to 1.0
        total_weight = sum(self.overall_scoring_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            issues.append(f"Overall scoring weights should sum to 1.0, got {total_weight}")
        
        return issues
