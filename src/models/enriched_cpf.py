"""
Enriched CPF (Candidate Profile File) model
Contains candidate-declared and government-verified information
"""
from datetime import date
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator

from .base import (
    BaseVerificationModel, 
    PersonalInfo, 
    Address, 
    EmploymentRecord, 
    EducationRecord
)


class GovernmentVerification(BaseModel):
    """Government verification status for various documents"""
    
    national_id_verified: bool = Field(default=False, description="National ID verification status")
    passport_verified: bool = Field(default=False, description="Passport verification status")
    address_verified: bool = Field(default=False, description="Address verification status")
    employment_verified: bool = Field(default=False, description="Employment verification status")
    education_verified: bool = Field(default=False, description="Education verification status")
    
    verification_date: Optional[date] = Field(None, description="Date of government verification")
    verification_authority: Optional[str] = Field(None, description="Verifying government authority")
    verification_reference: Optional[str] = Field(None, description="Government verification reference number")
    
    # Additional verification details
    verification_details: Optional[Dict[str, Any]] = Field(
        default_factory=dict, 
        description="Additional verification details"
    )


class CandidateDeclaredInfo(BaseModel):
    """Information declared by the candidate"""
    
    personal_info: PersonalInfo = Field(..., description="Personal information")
    current_address: Optional[Address] = Field(None, description="Current address")
    permanent_address: Optional[Address] = Field(None, description="Permanent address")
    
    employment_history: List[EmploymentRecord] = Field(
        default_factory=list, 
        description="Employment history"
    )
    
    education_history: List[EducationRecord] = Field(
        default_factory=list, 
        description="Education history"
    )
    
    # Additional candidate-declared fields
    references: Optional[List[Dict[str, str]]] = Field(
        default_factory=list, 
        description="Professional references"
    )
    
    skills: Optional[List[str]] = Field(
        default_factory=list, 
        description="Skills and competencies"
    )
    
    certifications: Optional[List[Dict[str, Any]]] = Field(
        default_factory=list, 
        description="Professional certifications"
    )
    
    @validator('employment_history')
    def validate_employment_history(cls, v):
        """Validate employment history for overlapping dates"""
        if len(v) <= 1:
            return v
            
        # Sort by start date
        sorted_employment = sorted(v, key=lambda x: x.start_date)
        
        for i in range(len(sorted_employment) - 1):
            current = sorted_employment[i]
            next_emp = sorted_employment[i + 1]
            
            # Check for overlapping employment periods
            if current.end_date and current.end_date > next_emp.start_date:
                # Allow some overlap (e.g., 30 days for transition)
                overlap_days = (current.end_date - next_emp.start_date).days
                if overlap_days > 30:
                    raise ValueError(
                        f"Employment overlap detected: {current.employer_name} and {next_emp.employer_name}"
                    )
        
        return v


class EnrichedCPF(BaseVerificationModel):
    """
    Enriched Candidate Profile File containing both candidate-declared 
    and government-verified information
    """
    
    candidate_id: str = Field(..., description="Unique candidate identifier")
    
    # Candidate declared information
    declared_info: CandidateDeclaredInfo = Field(..., description="Candidate declared information")
    
    # Government verification status
    government_verification: GovernmentVerification = Field(
        default_factory=GovernmentVerification,
        description="Government verification status"
    )
    
    # Data source information
    data_sources: Optional[List[str]] = Field(
        default_factory=list,
        description="Sources of information (e.g., 'candidate_form', 'government_db', 'previous_employer')"
    )
    
    # Confidence scores for different sections
    data_confidence: Optional[Dict[str, float]] = Field(
        default_factory=dict,
        description="Confidence scores for different data sections"
    )
    
    # Additional metadata
    collection_date: Optional[date] = Field(None, description="Date when information was collected")
    last_updated: Optional[date] = Field(None, description="Last update date")
    
    # Flags for data quality
    has_inconsistencies: bool = Field(default=False, description="Flag for data inconsistencies")
    inconsistency_notes: Optional[List[str]] = Field(
        default_factory=list,
        description="Notes about data inconsistencies"
    )
    
    def get_current_employment(self) -> Optional[EmploymentRecord]:
        """Get the current employment record (no end date)"""
        for employment in self.declared_info.employment_history:
            if employment.end_date is None:
                return employment
        return None
    
    def get_employment_by_employer(self, employer_name: str) -> List[EmploymentRecord]:
        """Get employment records for a specific employer"""
        return [
            emp for emp in self.declared_info.employment_history 
            if employer_name.lower() in emp.employer_name.lower()
        ]
    
    def get_total_experience_years(self) -> float:
        """Calculate total years of experience"""
        total_days = 0
        today = date.today()
        
        for employment in self.declared_info.employment_history:
            start = employment.start_date
            end = employment.end_date or today
            total_days += (end - start).days
        
        return total_days / 365.25  # Account for leap years
    
    def is_government_verified(self, field: str) -> bool:
        """Check if a specific field is government verified"""
        verification_map = {
            'national_id': self.government_verification.national_id_verified,
            'passport': self.government_verification.passport_verified,
            'address': self.government_verification.address_verified,
            'employment': self.government_verification.employment_verified,
            'education': self.government_verification.education_verified,
        }
        return verification_map.get(field, False)
    
    def add_inconsistency_note(self, note: str):
        """Add an inconsistency note"""
        if not self.inconsistency_notes:
            self.inconsistency_notes = []
        self.inconsistency_notes.append(note)
        self.has_inconsistencies = True
    
    def get_verification_summary(self) -> Dict[str, Any]:
        """Get a summary of verification status"""
        return {
            'candidate_id': self.candidate_id,
            'government_verified_fields': {
                'national_id': self.government_verification.national_id_verified,
                'passport': self.government_verification.passport_verified,
                'address': self.government_verification.address_verified,
                'employment': self.government_verification.employment_verified,
                'education': self.government_verification.education_verified,
            },
            'total_employment_records': len(self.declared_info.employment_history),
            'total_education_records': len(self.declared_info.education_history),
            'has_inconsistencies': self.has_inconsistencies,
            'data_sources': self.data_sources,
            'collection_date': self.collection_date,
        }
