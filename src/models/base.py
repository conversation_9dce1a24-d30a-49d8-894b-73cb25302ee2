"""
Base models and common data structures for the verification system
"""
from datetime import datetime, date
from typing import Optional, Dict, Any, List, Union
from enum import Enum
from pydantic import BaseModel, Field, validator
import uuid


class ColorCode(str, Enum):
    """Color codes for verification results"""
    GREEN = "Green"
    AMBER = "Amber"
    RED = "Red"


class VerificationStatus(str, Enum):
    """Status of verification process"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class BaseVerificationModel(BaseModel):
    """Base model with common fields for all verification models"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        }


class PersonalInfo(BaseModel):
    """Personal information structure"""
    
    full_name: str = Field(..., description="Full name of the candidate")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    date_of_birth: Optional[date] = Field(None, description="Date of birth")
    national_id: Optional[str] = Field(None, description="National ID number")
    passport_number: Optional[str] = Field(None, description="Passport number")
    email: Optional[str] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    
    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v


class Address(BaseModel):
    """Address information structure"""
    
    street: Optional[str] = Field(None, description="Street address")
    city: str = Field(..., description="City")
    state: Optional[str] = Field(None, description="State/Province")
    country: str = Field(..., description="Country")
    postal_code: Optional[str] = Field(None, description="Postal/ZIP code")
    
    def __str__(self) -> str:
        parts = [self.street, self.city, self.state, self.country, self.postal_code]
        return ", ".join(filter(None, parts))


class EmploymentRecord(BaseModel):
    """Employment record structure"""
    
    employer_name: str = Field(..., description="Name of the employer")
    job_title: str = Field(..., description="Job title/position")
    start_date: date = Field(..., description="Employment start date")
    end_date: Optional[date] = Field(None, description="Employment end date (None if current)")
    salary: Optional[float] = Field(None, description="Salary amount")
    currency: Optional[str] = Field("USD", description="Salary currency")
    location: Optional[Address] = Field(None, description="Work location")
    employment_type: Optional[str] = Field(None, description="Full-time, Part-time, Contract, etc.")
    
    @validator('end_date')
    def validate_end_date(cls, v, values):
        if v and 'start_date' in values and v < values['start_date']:
            raise ValueError('End date cannot be before start date')
        return v
    
    @validator('salary')
    def validate_salary(cls, v):
        if v is not None and v < 0:
            raise ValueError('Salary cannot be negative')
        return v


class EducationRecord(BaseModel):
    """Education record structure"""
    
    institution_name: str = Field(..., description="Name of the educational institution")
    degree: str = Field(..., description="Degree or qualification obtained")
    field_of_study: Optional[str] = Field(None, description="Field of study")
    start_date: Optional[date] = Field(None, description="Start date")
    end_date: Optional[date] = Field(None, description="End date")
    grade: Optional[str] = Field(None, description="Grade or GPA")
    location: Optional[Address] = Field(None, description="Institution location")


class VerificationResult(BaseModel):
    """Individual field verification result"""
    
    field_name: str = Field(..., description="Name of the verified field")
    color_code: ColorCode = Field(..., description="Color code result")
    remark: str = Field(..., description="Human-readable explanation")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0-1)")
    requires_human_review: bool = Field(default=False, description="Flag for human review")
    expected_value: Optional[Any] = Field(None, description="Expected value")
    actual_value: Optional[Any] = Field(None, description="Actual value found")
    variance: Optional[str] = Field(None, description="Description of variance")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details")


class OverallVerificationResult(BaseVerificationModel):
    """Complete verification result for a candidate"""
    
    candidate_id: str = Field(..., description="Unique candidate identifier")
    status: VerificationStatus = Field(default=VerificationStatus.PENDING)
    overall_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Overall confidence score")
    overall_color_code: Optional[ColorCode] = Field(None, description="Overall color code")
    field_results: List[VerificationResult] = Field(default_factory=list, description="Individual field results")
    summary: Optional[str] = Field(None, description="Summary of verification results")
    requires_human_review: bool = Field(default=False, description="Overall human review flag")
    processing_time_seconds: Optional[float] = Field(None, description="Time taken for verification")
    
    def add_field_result(self, result: VerificationResult):
        """Add a field verification result"""
        self.field_results.append(result)
        
    def calculate_overall_score(self, field_weights: Dict[str, float] = None):
        """Calculate overall score based on field results"""
        if not self.field_results:
            return 0.0
            
        if field_weights is None:
            # Equal weights if not provided
            total_score = sum(result.confidence_score for result in self.field_results)
            self.overall_score = total_score / len(self.field_results)
        else:
            weighted_score = 0.0
            total_weight = 0.0
            
            for result in self.field_results:
                weight = field_weights.get(result.field_name, 0.0)
                weighted_score += result.confidence_score * weight
                total_weight += weight
                
            self.overall_score = weighted_score / total_weight if total_weight > 0 else 0.0
        
        # Determine overall color code
        if self.overall_score >= 0.9:
            self.overall_color_code = ColorCode.GREEN
        elif self.overall_score >= 0.7:
            self.overall_color_code = ColorCode.AMBER
        else:
            self.overall_color_code = ColorCode.RED
            
        # Set human review flag
        self.requires_human_review = any(
            result.requires_human_review for result in self.field_results
        ) or self.overall_score < 0.6
        
        return self.overall_score
