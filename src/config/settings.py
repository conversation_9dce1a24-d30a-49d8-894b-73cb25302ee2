"""
Configuration settings for the ML Background Verification System
"""
import os
from typing import Dict, Any, List
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_workers: int = Field(default=4, env="API_WORKERS")
    
    # AWS Bedrock Configuration
    aws_region: str = Field(default="us-east-1", env="AWS_REGION")
    bedrock_model_id: str = Field(default="mistral.mistral-7b-instruct-v0:2", env="BEDROCK_MODEL_ID")
    bedrock_max_tokens: int = Field(default=2000, env="BEDROCK_MAX_TOKENS")
    bedrock_temperature: float = Field(default=0.1, env="BEDROCK_TEMPERATURE")

    # DynamoDB Configuration
    dynamodb_table_name: str = Field(default="Resume-PII-Processor-table", env="DYNAMODB_TABLE_NAME")

    # SQS Configuration
    sqs_queue_name: str = Field(default="verification-jobs-queue", env="SQS_QUEUE_NAME")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    
    # Database Configuration
    database_url: str = Field(default="sqlite:///./verification_results.db", env="DATABASE_URL")
    
    # Verification Thresholds
    confidence_threshold_green: float = Field(default=0.9, env="CONFIDENCE_THRESHOLD_GREEN")
    confidence_threshold_amber: float = Field(default=0.7, env="CONFIDENCE_THRESHOLD_AMBER")
    human_review_threshold: float = Field(default=0.6, env="HUMAN_REVIEW_THRESHOLD")
    
    # Security
    secret_key: str = Field(env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Rate Limiting
    rate_limit_per_minute: int = Field(default=100, env="RATE_LIMIT_PER_MINUTE")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    # Verification Rules
    date_tolerance_days: int = Field(default=30, env="DATE_TOLERANCE_DAYS")
    salary_tolerance_percent: float = Field(default=10.0, env="SALARY_TOLERANCE_PERCENT")
    name_similarity_threshold: float = Field(default=0.8, env="NAME_SIMILARITY_THRESHOLD")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


class VerificationRules:
    """Default verification rules and thresholds"""
    
    # Field weights for overall scoring
    FIELD_WEIGHTS = {
        "employment_dates": 0.25,
        "employer_name": 0.20,
        "salary": 0.20,
        "location": 0.15,
        "identification": 0.20
    }
    
    # Color code thresholds
    COLOR_THRESHOLDS = {
        "green": 0.9,
        "amber": 0.7,
        "red": 0.0
    }
    
    # Field-specific validation rules
    FIELD_RULES = {
        "employment_dates": {
            "tolerance_days": 30,
            "required": True,
            "weight": 0.25
        },
        "employer_name": {
            "similarity_threshold": 0.8,
            "required": True,
            "weight": 0.20
        },
        "salary": {
            "tolerance_percent": 10.0,
            "required": False,
            "weight": 0.20
        },
        "location": {
            "similarity_threshold": 0.7,
            "required": False,
            "weight": 0.15
        },
        "identification": {
            "exact_match": True,
            "required": True,
            "weight": 0.20
        }
    }


# Global settings instance
settings = Settings()
verification_rules = VerificationRules()
