"""
Middleware for API logging, rate limiting, and other cross-cutting concerns
"""
import time
import json
from typing import Callable, Dict, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque

from fastapi import FastAPI, Request, Response, HTTPException, status
from fastapi.middleware.base import BaseHTTPMiddleware
from loguru import logger
import uuid

from config.settings import settings


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Start timing
        start_time = time.time()
        
        # Log request
        logger.info(
            f"Request started",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host,
                "user_agent": request.headers.get("user-agent"),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            logger.info(
                f"Request completed",
                extra={
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "process_time": process_time,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # Add headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log error
            logger.error(
                f"Request failed",
                extra={
                    "request_id": request_id,
                    "error": str(e),
                    "process_time": process_time,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # Re-raise the exception
            raise


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Simple in-memory rate limiting middleware"""
    
    def __init__(self, app: FastAPI, requests_per_minute: int = 100):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.client_requests: Dict[str, deque] = defaultdict(deque)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Get client IP
        client_ip = request.client.host
        
        # Current time
        now = datetime.utcnow()
        
        # Clean old requests (older than 1 minute)
        cutoff_time = now - timedelta(minutes=1)
        client_queue = self.client_requests[client_ip]
        
        while client_queue and client_queue[0] < cutoff_time:
            client_queue.popleft()
        
        # Check rate limit
        if len(client_queue) >= self.requests_per_minute:
            logger.warning(
                f"Rate limit exceeded for client {client_ip}",
                extra={
                    "client_ip": client_ip,
                    "requests_count": len(client_queue),
                    "limit": self.requests_per_minute
                }
            )
            
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Rate limit exceeded",
                    "message": f"Maximum {self.requests_per_minute} requests per minute allowed",
                    "retry_after": 60
                }
            )
        
        # Add current request
        client_queue.append(now)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(self.requests_per_minute - len(client_queue))
        response.headers["X-RateLimit-Reset"] = str(int((now + timedelta(minutes=1)).timestamp()))
        
        return response


class SecurityMiddleware(BaseHTTPMiddleware):
    """Security headers and basic security checks"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Basic security checks
        if request.method == "POST" and request.url.path in ["/verify", "/batch-verify"]:
            # Check content type for POST requests
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith("application/json"):
                raise HTTPException(
                    status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                    detail="Content-Type must be application/json"
                )
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting metrics"""
    
    def __init__(self, app: FastAPI):
        super().__init__(app)
        self.request_count = defaultdict(int)
        self.response_times = defaultdict(list)
        self.error_count = defaultdict(int)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Record metrics
            endpoint = f"{request.method} {request.url.path}"
            process_time = time.time() - start_time
            
            self.request_count[endpoint] += 1
            self.response_times[endpoint].append(process_time)
            
            # Keep only last 1000 response times per endpoint
            if len(self.response_times[endpoint]) > 1000:
                self.response_times[endpoint] = self.response_times[endpoint][-1000:]
            
            return response
            
        except Exception as e:
            # Record error
            endpoint = f"{request.method} {request.url.path}"
            self.error_count[endpoint] += 1
            raise
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        metrics = {
            "request_counts": dict(self.request_count),
            "error_counts": dict(self.error_count),
            "average_response_times": {}
        }
        
        for endpoint, times in self.response_times.items():
            if times:
                metrics["average_response_times"][endpoint] = sum(times) / len(times)
        
        return metrics


# Global metrics instance
metrics_middleware = None


def setup_logging(app: FastAPI):
    """Setup logging middleware"""
    app.add_middleware(LoggingMiddleware)
    
    # Configure loguru
    logger.remove()  # Remove default handler
    
    if settings.log_format == "json":
        logger.add(
            "logs/api.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message} | {extra}",
            level=settings.log_level,
            rotation="1 day",
            retention="30 days",
            serialize=True
        )
    else:
        logger.add(
            "logs/api.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
            level=settings.log_level,
            rotation="1 day",
            retention="30 days"
        )
    
    # Also log to console
    logger.add(
        lambda msg: print(msg, end=""),
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | <cyan>{message}</cyan>"
    )


def setup_rate_limiting(app: FastAPI):
    """Setup rate limiting middleware"""
    app.add_middleware(
        RateLimitingMiddleware,
        requests_per_minute=settings.rate_limit_per_minute
    )


def setup_security(app: FastAPI):
    """Setup security middleware"""
    app.add_middleware(SecurityMiddleware)


def setup_metrics(app: FastAPI):
    """Setup metrics middleware"""
    global metrics_middleware
    metrics_middleware = MetricsMiddleware(app)
    app.add_middleware(MetricsMiddleware)


def get_metrics() -> Dict[str, Any]:
    """Get current metrics"""
    if metrics_middleware:
        return metrics_middleware.get_metrics()
    return {}


# Exception handlers
async def validation_exception_handler(request: Request, exc: Exception):
    """Handle validation exceptions"""
    logger.error(f"Validation error: {str(exc)}")
    return Response(
        content=json.dumps({
            "error": "Validation Error",
            "message": str(exc),
            "timestamp": datetime.utcnow().isoformat(),
            "request_id": getattr(request.state, "request_id", None)
        }),
        status_code=422,
        media_type="application/json"
    )


async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return Response(
        content=json.dumps({
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "timestamp": datetime.utcnow().isoformat(),
            "request_id": getattr(request.state, "request_id", None)
        }),
        status_code=500,
        media_type="application/json"
    )
