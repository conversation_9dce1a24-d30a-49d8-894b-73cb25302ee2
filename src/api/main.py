"""
Main FastAPI application for the ML Background Verification System
"""
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
import uuid

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
import uvicorn

from ..models import (
    EnrichedCPF,
    ClientInput,
    ValidationMatrix,
    OverallVerificationResult,
    VerificationStatus
)
from ..verification import VerificationEngine
from ..scoring import VerificationScorer
from .models import (
    VerificationRequest,
    VerificationResponse,
    StatusResponse,
    HealthResponse
)
from .middleware import setup_logging, setup_rate_limiting
from config.settings import settings

# Initialize FastAPI app
app = FastAPI(
    title="ML Background Verification System",
    description="AI-powered automation system for candidate background verification",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup middleware
setup_logging(app)
setup_rate_limiting(app)

# Global instances
verification_engine = VerificationEngine()
scorer = VerificationScorer()

# In-memory storage for demo (use database in production)
verification_jobs: Dict[str, Dict[str, Any]] = {}


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting ML Background Verification System")
    logger.info(f"API running on {settings.api_host}:{settings.api_port}")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down ML Background Verification System")


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow(),
        version="1.0.0",
        services={
            "verification_engine": "operational",
            "scoring_system": "operational",
            "api": "operational"
        }
    )


@app.post("/verify", response_model=VerificationResponse)
async def submit_verification(
    request: VerificationRequest,
    background_tasks: BackgroundTasks
):
    """
    Submit a verification request for processing
    """
    try:
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Validate request
        validation_errors = _validate_verification_request(request)
        if validation_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"errors": validation_errors}
            )
        
        # Store job information
        verification_jobs[job_id] = {
            "job_id": job_id,
            "status": VerificationStatus.PENDING,
            "created_at": datetime.utcnow(),
            "request": request,
            "result": None,
            "error": None
        }
        
        # Start background verification
        background_tasks.add_task(
            process_verification,
            job_id,
            request.enriched_cpf,
            request.client_input,
            request.validation_matrix
        )
        
        logger.info(f"Verification job {job_id} submitted for candidate {request.enriched_cpf.candidate_id}")
        
        return VerificationResponse(
            job_id=job_id,
            status=VerificationStatus.PENDING,
            message="Verification request submitted successfully",
            estimated_completion_time=60  # seconds
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting verification request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.get("/status/{job_id}", response_model=StatusResponse)
async def get_verification_status(job_id: str):
    """
    Get the status of a verification job
    """
    try:
        if job_id not in verification_jobs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Verification job {job_id} not found"
            )
        
        job = verification_jobs[job_id]
        
        return StatusResponse(
            job_id=job_id,
            status=job["status"],
            created_at=job["created_at"],
            completed_at=job.get("completed_at"),
            progress_percentage=_calculate_progress(job),
            message=_get_status_message(job)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting verification status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.get("/results/{job_id}")
async def get_verification_results(job_id: str):
    """
    Get the results of a completed verification job
    """
    try:
        if job_id not in verification_jobs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Verification job {job_id} not found"
            )
        
        job = verification_jobs[job_id]
        
        if job["status"] == VerificationStatus.PENDING:
            raise HTTPException(
                status_code=status.HTTP_202_ACCEPTED,
                detail="Verification is still in progress"
            )
        
        if job["status"] == VerificationStatus.IN_PROGRESS:
            raise HTTPException(
                status_code=status.HTTP_202_ACCEPTED,
                detail="Verification is still in progress"
            )
        
        if job["status"] == VerificationStatus.FAILED:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Verification failed: {job.get('error', 'Unknown error')}"
            )
        
        # Return results
        result = job["result"]
        if not result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Verification completed but no results available"
            )
        
        # Generate comprehensive summary
        summary = scorer.generate_verification_summary(result)
        
        return {
            "job_id": job_id,
            "status": job["status"],
            "candidate_id": result.candidate_id,
            "verification_results": {
                "overall_score": result.overall_score,
                "overall_color_code": result.overall_color_code.value,
                "requires_human_review": result.requires_human_review,
                "summary": result.summary,
                "field_results": [
                    {
                        "field_name": r.field_name,
                        "color_code": r.color_code.value,
                        "confidence_score": r.confidence_score,
                        "remark": r.remark,
                        "expected_value": r.expected_value,
                        "actual_value": r.actual_value,
                        "variance": r.variance,
                        "requires_human_review": r.requires_human_review
                    }
                    for r in result.field_results
                ]
            },
            "summary": summary,
            "processing_time_seconds": result.processing_time_seconds,
            "completed_at": job.get("completed_at")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting verification results: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.delete("/jobs/{job_id}")
async def cancel_verification_job(job_id: str):
    """
    Cancel a verification job
    """
    try:
        if job_id not in verification_jobs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Verification job {job_id} not found"
            )
        
        job = verification_jobs[job_id]
        
        if job["status"] in [VerificationStatus.COMPLETED, VerificationStatus.FAILED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot cancel completed or failed job"
            )
        
        # Mark as cancelled (in production, you'd need to actually stop the background task)
        job["status"] = VerificationStatus.FAILED
        job["error"] = "Job cancelled by user"
        job["completed_at"] = datetime.utcnow()
        
        logger.info(f"Verification job {job_id} cancelled")
        
        return {"message": f"Verification job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling verification job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


async def process_verification(
    job_id: str,
    enriched_cpf: EnrichedCPF,
    client_input: ClientInput,
    validation_matrix: ValidationMatrix
):
    """
    Background task to process verification
    """
    try:
        logger.info(f"Starting verification processing for job {job_id}")
        
        # Update status
        verification_jobs[job_id]["status"] = VerificationStatus.IN_PROGRESS
        
        # Perform verification
        result = await verification_engine.verify_candidate(
            enriched_cpf,
            client_input,
            validation_matrix
        )
        
        # Update job with results
        verification_jobs[job_id].update({
            "status": VerificationStatus.COMPLETED,
            "result": result,
            "completed_at": datetime.utcnow()
        })
        
        logger.info(f"Verification completed for job {job_id}")
        
    except Exception as e:
        logger.error(f"Verification failed for job {job_id}: {str(e)}")
        verification_jobs[job_id].update({
            "status": VerificationStatus.FAILED,
            "error": str(e),
            "completed_at": datetime.utcnow()
        })


def _validate_verification_request(request: VerificationRequest) -> list:
    """Validate verification request"""
    errors = []
    
    # Validate client input requirements
    client_errors = request.client_input.validate_requirements()
    errors.extend(client_errors)
    
    # Validate validation matrix
    matrix_errors = request.validation_matrix.validate_matrix()
    errors.extend(matrix_errors)
    
    return errors


def _calculate_progress(job: Dict[str, Any]) -> int:
    """Calculate job progress percentage"""
    status = job["status"]
    
    if status == VerificationStatus.PENDING:
        return 0
    elif status == VerificationStatus.IN_PROGRESS:
        # In a real implementation, you'd track actual progress
        return 50
    elif status in [VerificationStatus.COMPLETED, VerificationStatus.FAILED]:
        return 100
    else:
        return 0


def _get_status_message(job: Dict[str, Any]) -> str:
    """Get human-readable status message"""
    status = job["status"]
    
    if status == VerificationStatus.PENDING:
        return "Verification request is queued for processing"
    elif status == VerificationStatus.IN_PROGRESS:
        return "Verification is currently in progress"
    elif status == VerificationStatus.COMPLETED:
        return "Verification completed successfully"
    elif status == VerificationStatus.FAILED:
        return f"Verification failed: {job.get('error', 'Unknown error')}"
    else:
        return "Unknown status"


if __name__ == "__main__":
    uvicorn.run(
        "src.api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        workers=settings.api_workers,
        reload=True
    )
