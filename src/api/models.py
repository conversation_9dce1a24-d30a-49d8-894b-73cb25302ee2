"""
API models for request and response handling
"""
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

from ..models import (
    EnrichedCPF,
    ClientInput,
    ValidationMatrix,
    VerificationStatus
)


class VerificationRequest(BaseModel):
    """Request model for verification submission"""
    
    enriched_cpf: EnrichedCPF = Field(..., description="Enriched candidate profile file")
    client_input: ClientInput = Field(..., description="Client input with expected values")
    validation_matrix: ValidationMatrix = Field(..., description="Validation rules matrix")
    
    # Optional metadata
    priority: Optional[str] = Field("medium", description="Request priority: low, medium, high, critical")
    callback_url: Optional[str] = Field(None, description="URL to call when verification is complete")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        schema_extra = {
            "example": {
                "enriched_cpf": {
                    "candidate_id": "CAND_12345",
                    "declared_info": {
                        "personal_info": {
                            "full_name": "<PERSON>",
                            "email": "<EMAIL>",
                            "phone": "+1234567890"
                        },
                        "employment_history": [
                            {
                                "employer_name": "Tech Corp",
                                "job_title": "Software Engineer",
                                "start_date": "2020-01-01",
                                "end_date": "2023-12-31",
                                "salary": 75000,
                                "currency": "USD"
                            }
                        ]
                    },
                    "government_verification": {
                        "national_id_verified": True,
                        "employment_verified": False
                    }
                },
                "client_input": {
                    "client_id": "CLIENT_001",
                    "candidate_id": "CAND_12345",
                    "expected_personal_info": {
                        "full_name": "John Doe",
                        "email": "<EMAIL>"
                    },
                    "expected_employment": [
                        {
                            "employer_name": "Tech Corp",
                            "min_salary": 70000,
                            "max_salary": 80000
                        }
                    ]
                },
                "validation_matrix": {
                    "company_id": "CLIENT_001",
                    "matrix_name": "Standard Verification",
                    "employment_rules": {
                        "employer_name_rule": {
                            "field_name": "employer_name",
                            "field_type": "text",
                            "match_type": "fuzzy",
                            "similarity_threshold": 0.8
                        }
                    }
                }
            }
        }


class VerificationResponse(BaseModel):
    """Response model for verification submission"""
    
    job_id: str = Field(..., description="Unique job identifier")
    status: VerificationStatus = Field(..., description="Current verification status")
    message: str = Field(..., description="Human-readable message")
    estimated_completion_time: Optional[int] = Field(None, description="Estimated completion time in seconds")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Job creation timestamp")


class StatusResponse(BaseModel):
    """Response model for status check"""
    
    job_id: str = Field(..., description="Job identifier")
    status: VerificationStatus = Field(..., description="Current verification status")
    created_at: datetime = Field(..., description="Job creation timestamp")
    completed_at: Optional[datetime] = Field(None, description="Job completion timestamp")
    progress_percentage: int = Field(..., ge=0, le=100, description="Progress percentage")
    message: str = Field(..., description="Status message")
    
    # Optional detailed progress information
    current_step: Optional[str] = Field(None, description="Current processing step")
    steps_completed: Optional[int] = Field(None, description="Number of steps completed")
    total_steps: Optional[int] = Field(None, description="Total number of steps")


class FieldResultResponse(BaseModel):
    """Response model for individual field verification result"""
    
    field_name: str = Field(..., description="Name of the verified field")
    color_code: str = Field(..., description="Color code: Green, Amber, or Red")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    remark: str = Field(..., description="Human-readable explanation")
    expected_value: Optional[Any] = Field(None, description="Expected value")
    actual_value: Optional[Any] = Field(None, description="Actual value found")
    variance: Optional[str] = Field(None, description="Description of variance")
    requires_human_review: bool = Field(..., description="Whether human review is required")


class VerificationResultResponse(BaseModel):
    """Response model for complete verification results"""
    
    job_id: str = Field(..., description="Job identifier")
    status: VerificationStatus = Field(..., description="Verification status")
    candidate_id: str = Field(..., description="Candidate identifier")
    
    # Overall results
    overall_score: float = Field(..., ge=0.0, le=1.0, description="Overall confidence score")
    overall_color_code: str = Field(..., description="Overall color code")
    requires_human_review: bool = Field(..., description="Whether human review is required")
    summary: str = Field(..., description="Verification summary")
    
    # Individual field results
    field_results: List[FieldResultResponse] = Field(..., description="Individual field results")
    
    # Processing information
    processing_time_seconds: Optional[float] = Field(None, description="Processing time")
    completed_at: Optional[datetime] = Field(None, description="Completion timestamp")
    
    # Additional analysis
    risk_level: Optional[str] = Field(None, description="Risk level: Low, Medium, High, Critical")
    recommendations: Optional[Dict[str, Any]] = Field(None, description="Recommendations")


class HealthResponse(BaseModel):
    """Response model for health check"""
    
    status: str = Field(..., description="Overall system status")
    timestamp: datetime = Field(..., description="Health check timestamp")
    version: str = Field(..., description="System version")
    services: Dict[str, str] = Field(..., description="Status of individual services")
    
    # Optional detailed health information
    uptime_seconds: Optional[float] = Field(None, description="System uptime in seconds")
    memory_usage: Optional[Dict[str, Any]] = Field(None, description="Memory usage statistics")
    active_jobs: Optional[int] = Field(None, description="Number of active verification jobs")


class ErrorResponse(BaseModel):
    """Response model for errors"""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request identifier for tracking")


class BatchVerificationRequest(BaseModel):
    """Request model for batch verification"""
    
    requests: List[VerificationRequest] = Field(..., description="List of verification requests")
    batch_id: Optional[str] = Field(None, description="Optional batch identifier")
    priority: Optional[str] = Field("medium", description="Batch priority")
    callback_url: Optional[str] = Field(None, description="URL to call when batch is complete")
    
    class Config:
        schema_extra = {
            "example": {
                "requests": [
                    # Multiple VerificationRequest objects
                ],
                "batch_id": "BATCH_001",
                "priority": "high",
                "callback_url": "https://client.example.com/webhook/verification-complete"
            }
        }


class BatchVerificationResponse(BaseModel):
    """Response model for batch verification submission"""
    
    batch_id: str = Field(..., description="Batch identifier")
    job_ids: List[str] = Field(..., description="List of individual job identifiers")
    total_requests: int = Field(..., description="Total number of requests in batch")
    estimated_completion_time: Optional[int] = Field(None, description="Estimated completion time in seconds")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Batch creation timestamp")


class BatchStatusResponse(BaseModel):
    """Response model for batch status check"""
    
    batch_id: str = Field(..., description="Batch identifier")
    total_requests: int = Field(..., description="Total number of requests")
    completed_requests: int = Field(..., description="Number of completed requests")
    failed_requests: int = Field(..., description="Number of failed requests")
    progress_percentage: int = Field(..., ge=0, le=100, description="Overall progress percentage")
    
    # Individual job statuses
    job_statuses: List[StatusResponse] = Field(..., description="Status of individual jobs")
    
    created_at: datetime = Field(..., description="Batch creation timestamp")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")


class WebhookPayload(BaseModel):
    """Payload model for webhook notifications"""
    
    event_type: str = Field(..., description="Type of event: verification_complete, verification_failed, etc.")
    job_id: str = Field(..., description="Job identifier")
    candidate_id: str = Field(..., description="Candidate identifier")
    status: VerificationStatus = Field(..., description="Verification status")
    
    # Optional result data (included for completed verifications)
    result: Optional[VerificationResultResponse] = Field(None, description="Verification results")
    
    # Event metadata
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    webhook_id: str = Field(..., description="Unique webhook identifier")
    
    class Config:
        schema_extra = {
            "example": {
                "event_type": "verification_complete",
                "job_id": "job_12345",
                "candidate_id": "CAND_12345",
                "status": "completed",
                "result": {
                    # VerificationResultResponse object
                },
                "timestamp": "2023-12-01T10:00:00Z",
                "webhook_id": "webhook_67890"
            }
        }
