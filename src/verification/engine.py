"""
Core Verification Engine
Main engine that processes the three inputs and applies comparison rules
"""
import asyncio
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Tuple
from loguru import logger

from ..models import (
    EnrichedCPF,
    ClientInput,
    ValidationMatrix,
    VerificationResult,
    OverallVerificationResult,
    ColorCode,
    VerificationStatus,
    ValidationRule,
    MatchType
)
from ..prompt_engineering.prompt_manager import PromptManager
from ..prompt_engineering.bedrock_client import BedrockClient
from ..scoring.scorer import VerificationScorer
from .field_validators import FieldValidator


class VerificationEngine:
    """
    Main verification engine that orchestrates the verification process
    """
    
    def __init__(self):
        self.prompt_manager = PromptManager()
        self.scorer = VerificationScorer()
        self.field_validator = FieldValidator()
        self.bedrock_client = BedrockClient()
        
    async def verify_candidate(
        self,
        enriched_cpf: EnrichedCPF,
        client_input: ClientInput,
        validation_matrix: ValidationMatrix
    ) -> OverallVerificationResult:
        """
        Main verification method that processes all inputs and returns results
        """
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Starting verification for candidate {enriched_cpf.candidate_id}")
            
            # Initialize result object
            result = OverallVerificationResult(
                candidate_id=enriched_cpf.candidate_id,
                status=VerificationStatus.IN_PROGRESS
            )
            
            # Verify different categories
            employment_results = await self._verify_employment(
                enriched_cpf, client_input, validation_matrix
            )
            
            education_results = await self._verify_education(
                enriched_cpf, client_input, validation_matrix
            )
            
            personal_info_results = await self._verify_personal_info(
                enriched_cpf, client_input, validation_matrix
            )
            
            # Combine all results
            all_results = employment_results + education_results + personal_info_results
            
            for field_result in all_results:
                result.add_field_result(field_result)
            
            # Calculate overall score
            result.calculate_overall_score(validation_matrix.overall_scoring_weights)
            
            # Generate summary
            result.summary = self._generate_summary(result, validation_matrix)
            
            # Set completion status
            result.status = VerificationStatus.COMPLETED
            result.processing_time_seconds = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(
                f"Verification completed for candidate {enriched_cpf.candidate_id}. "
                f"Overall score: {result.overall_score:.2f}, "
                f"Color: {result.overall_color_code}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Verification failed for candidate {enriched_cpf.candidate_id}: {str(e)}")
            result.status = VerificationStatus.FAILED
            result.summary = f"Verification failed: {str(e)}"
            return result
    
    async def _verify_employment(
        self,
        enriched_cpf: EnrichedCPF,
        client_input: ClientInput,
        validation_matrix: ValidationMatrix
    ) -> List[VerificationResult]:
        """Verify employment information"""
        results = []
        
        logger.info("Starting employment verification")
        
        # Get employment data
        actual_employment = enriched_cpf.declared_info.employment_history
        expected_employment = client_input.expected_employment
        
        if not expected_employment:
            logger.info("No expected employment data provided")
            return results
        
        # Verify each expected employment
        for expected_emp in expected_employment:
            if not expected_emp.must_verify:
                continue
                
            # Find matching employment in actual data
            matching_employment = self._find_matching_employment(
                expected_emp, actual_employment
            )
            
            if matching_employment:
                # Verify individual fields
                emp_results = await self._verify_employment_fields(
                    expected_emp, matching_employment, validation_matrix
                )
                results.extend(emp_results)
            else:
                # Employment not found
                result = VerificationResult(
                    field_name=f"employment_{expected_emp.employer_name}",
                    color_code=ColorCode.RED,
                    remark=f"Expected employment at {expected_emp.employer_name} not found",
                    confidence_score=0.0,
                    requires_human_review=True,
                    expected_value=expected_emp.employer_name,
                    actual_value=None
                )
                results.append(result)
        
        return results
    
    async def _verify_education(
        self,
        enriched_cpf: EnrichedCPF,
        client_input: ClientInput,
        validation_matrix: ValidationMatrix
    ) -> List[VerificationResult]:
        """Verify education information"""
        results = []
        
        logger.info("Starting education verification")
        
        # Get education data
        actual_education = enriched_cpf.declared_info.education_history
        expected_education = client_input.expected_education
        
        if not expected_education:
            logger.info("No expected education data provided")
            return results
        
        # Verify each expected education
        for expected_edu in expected_education:
            if not expected_edu.must_verify:
                continue
                
            # Find matching education in actual data
            matching_education = self._find_matching_education(
                expected_edu, actual_education
            )
            
            if matching_education:
                # Verify individual fields
                edu_results = await self._verify_education_fields(
                    expected_edu, matching_education, validation_matrix
                )
                results.extend(edu_results)
            else:
                # Education not found
                result = VerificationResult(
                    field_name=f"education_{expected_edu.institution_name}",
                    color_code=ColorCode.RED,
                    remark=f"Expected education at {expected_edu.institution_name} not found",
                    confidence_score=0.0,
                    requires_human_review=True,
                    expected_value=expected_edu.institution_name,
                    actual_value=None
                )
                results.append(result)
        
        return results
    
    async def _verify_personal_info(
        self,
        enriched_cpf: EnrichedCPF,
        client_input: ClientInput,
        validation_matrix: ValidationMatrix
    ) -> List[VerificationResult]:
        """Verify personal information"""
        results = []
        
        logger.info("Starting personal information verification")
        
        actual_info = enriched_cpf.declared_info.personal_info
        expected_info = client_input.expected_personal_info
        
        # Verify name
        name_result = await self._verify_field(
            "name",
            expected_info.full_name,
            actual_info.full_name,
            validation_matrix.personal_info_rules.name_rule
        )
        results.append(name_result)
        
        # Verify date of birth
        if expected_info.date_of_birth and actual_info.date_of_birth:
            dob_result = await self._verify_field(
                "date_of_birth",
                expected_info.date_of_birth,
                actual_info.date_of_birth,
                validation_matrix.personal_info_rules.date_of_birth_rule
            )
            results.append(dob_result)
        
        # Verify national ID
        if expected_info.national_id and actual_info.national_id:
            id_result = await self._verify_field(
                "national_id",
                expected_info.national_id,
                actual_info.national_id,
                validation_matrix.personal_info_rules.national_id_rule
            )
            results.append(id_result)
        
        # Verify email
        if expected_info.email and actual_info.email:
            email_result = await self._verify_field(
                "email",
                expected_info.email,
                actual_info.email,
                validation_matrix.personal_info_rules.email_rule
            )
            results.append(email_result)
        
        return results
    
    async def _verify_employment_fields(
        self,
        expected_emp,
        actual_emp,
        validation_matrix: ValidationMatrix
    ) -> List[VerificationResult]:
        """Verify individual employment fields"""
        results = []
        
        # Verify employer name
        employer_result = await self._verify_field(
            "employer_name",
            expected_emp.employer_name,
            actual_emp.employer_name,
            validation_matrix.employment_rules.employer_name_rule
        )
        results.append(employer_result)
        
        # Verify start date
        if expected_emp.start_date:
            start_date_result = await self._verify_field(
                "employment_start_date",
                expected_emp.start_date,
                actual_emp.start_date,
                validation_matrix.employment_rules.start_date_rule
            )
            results.append(start_date_result)
        
        # Verify end date
        if expected_emp.end_date and actual_emp.end_date:
            end_date_result = await self._verify_field(
                "employment_end_date",
                expected_emp.end_date,
                actual_emp.end_date,
                validation_matrix.employment_rules.end_date_rule
            )
            results.append(end_date_result)
        
        # Verify salary
        if expected_emp.min_salary and actual_emp.salary:
            salary_result = await self._verify_salary(
                expected_emp, actual_emp, validation_matrix.employment_rules.salary_rule
            )
            results.append(salary_result)
        
        return results
    
    async def _verify_education_fields(
        self,
        expected_edu,
        actual_edu,
        validation_matrix: ValidationMatrix
    ) -> List[VerificationResult]:
        """Verify individual education fields"""
        results = []
        
        # Verify institution name
        institution_result = await self._verify_field(
            "institution_name",
            expected_edu.institution_name,
            actual_edu.institution_name,
            validation_matrix.education_rules.institution_name_rule
        )
        results.append(institution_result)
        
        # Verify degree
        degree_result = await self._verify_field(
            "degree",
            expected_edu.degree,
            actual_edu.degree,
            validation_matrix.education_rules.degree_rule
        )
        results.append(degree_result)
        
        return results
    
    def _find_matching_employment(self, expected_emp, actual_employment_list):
        """Find matching employment record"""
        for actual_emp in actual_employment_list:
            if self.field_validator.is_similar_text(
                expected_emp.employer_name, 
                actual_emp.employer_name, 
                threshold=0.8
            ):
                return actual_emp
        return None
    
    def _find_matching_education(self, expected_edu, actual_education_list):
        """Find matching education record"""
        for actual_edu in actual_education_list:
            if self.field_validator.is_similar_text(
                expected_edu.institution_name,
                actual_edu.institution_name,
                threshold=0.8
            ):
                return actual_edu
        return None

    async def _verify_field(
        self,
        field_name: str,
        expected_value: Any,
        actual_value: Any,
        validation_rule: Optional[ValidationRule]
    ) -> VerificationResult:
        """Verify a single field using the validation rule"""

        if validation_rule is None:
            # Default verification without specific rule
            score = 1.0 if expected_value == actual_value else 0.0
            color_code = ColorCode.GREEN if score == 1.0 else ColorCode.RED
            remark = "Exact match" if score == 1.0 else "No match"
        else:
            # Use validation rule
            score = await self.field_validator.validate_field(
                expected_value, actual_value, validation_rule
            )
            color_code = self._get_color_code(score, validation_rule)
            remark = self._generate_field_remark(
                field_name, expected_value, actual_value, score, validation_rule
            )

        return VerificationResult(
            field_name=field_name,
            color_code=color_code,
            remark=remark,
            confidence_score=score,
            requires_human_review=score < 0.6,
            expected_value=expected_value,
            actual_value=actual_value
        )

    async def _verify_salary(
        self,
        expected_emp,
        actual_emp,
        validation_rule: Optional[ValidationRule]
    ) -> VerificationResult:
        """Verify salary with range checking"""

        actual_salary = actual_emp.salary
        min_salary = expected_emp.min_salary
        max_salary = expected_emp.max_salary

        if actual_salary is None:
            return VerificationResult(
                field_name="salary",
                color_code=ColorCode.RED,
                remark="Salary information not provided",
                confidence_score=0.0,
                requires_human_review=True,
                expected_value=f"{min_salary}-{max_salary}" if max_salary else str(min_salary),
                actual_value=None
            )

        # Check if salary is within expected range
        in_range = True
        if min_salary and actual_salary < min_salary:
            in_range = False
        if max_salary and actual_salary > max_salary:
            in_range = False

        if in_range:
            score = 1.0
            color_code = ColorCode.GREEN
            remark = "Salary within expected range"
        else:
            # Calculate how far off the salary is
            if min_salary and actual_salary < min_salary:
                variance_percent = ((min_salary - actual_salary) / min_salary) * 100
            elif max_salary and actual_salary > max_salary:
                variance_percent = ((actual_salary - max_salary) / max_salary) * 100
            else:
                variance_percent = 0

            # Score based on variance
            if variance_percent <= 10:
                score = 0.8
                color_code = ColorCode.AMBER
                remark = f"Salary slightly outside range ({variance_percent:.1f}% variance)"
            elif variance_percent <= 25:
                score = 0.5
                color_code = ColorCode.AMBER
                remark = f"Salary moderately outside range ({variance_percent:.1f}% variance)"
            else:
                score = 0.2
                color_code = ColorCode.RED
                remark = f"Salary significantly outside range ({variance_percent:.1f}% variance)"

        return VerificationResult(
            field_name="salary",
            color_code=color_code,
            remark=remark,
            confidence_score=score,
            requires_human_review=score < 0.7,
            expected_value=f"{min_salary}-{max_salary}" if max_salary else str(min_salary),
            actual_value=actual_salary,
            variance=f"{variance_percent:.1f}%" if not in_range else None
        )

    def _get_color_code(self, score: float, validation_rule: ValidationRule) -> ColorCode:
        """Get color code based on score and validation rule"""
        if score >= validation_rule.green_threshold:
            return ColorCode.GREEN
        elif score >= validation_rule.amber_threshold:
            return ColorCode.AMBER
        else:
            return ColorCode.RED

    def _generate_field_remark(
        self,
        field_name: str,
        expected_value: Any,
        actual_value: Any,
        score: float,
        validation_rule: ValidationRule
    ) -> str:
        """Generate human-readable remark for field verification"""

        if score >= validation_rule.green_threshold:
            if validation_rule.match_type == MatchType.EXACT:
                return f"{field_name.replace('_', ' ').title()} matches exactly"
            else:
                return f"{field_name.replace('_', ' ').title()} matches with high confidence"

        elif score >= validation_rule.amber_threshold:
            if validation_rule.match_type == MatchType.FUZZY:
                return f"{field_name.replace('_', ' ').title()} has minor differences but is likely correct"
            elif validation_rule.match_type == MatchType.DATE_TOLERANCE:
                return f"{field_name.replace('_', ' ').title()} is within acceptable date tolerance"
            else:
                return f"{field_name.replace('_', ' ').title()} has minor discrepancies"

        else:
            return f"{field_name.replace('_', ' ').title()} does not match expected value"

    def _generate_summary(
        self,
        result: OverallVerificationResult,
        validation_matrix: ValidationMatrix
    ) -> str:
        """Generate overall verification summary"""

        total_fields = len(result.field_results)
        green_count = sum(1 for r in result.field_results if r.color_code == ColorCode.GREEN)
        amber_count = sum(1 for r in result.field_results if r.color_code == ColorCode.AMBER)
        red_count = sum(1 for r in result.field_results if r.color_code == ColorCode.RED)

        summary_parts = [
            f"Verification completed for {total_fields} fields.",
            f"Results: {green_count} Green, {amber_count} Amber, {red_count} Red.",
            f"Overall confidence score: {result.overall_score:.2f}",
            f"Overall classification: {result.overall_color_code.value}"
        ]

        if result.requires_human_review:
            summary_parts.append("Human review recommended due to low confidence or critical discrepancies.")

        # Add critical issues
        critical_issues = [
            r for r in result.field_results
            if r.color_code == ColorCode.RED and r.requires_human_review
        ]

        if critical_issues:
            issue_fields = [r.field_name for r in critical_issues]
            summary_parts.append(f"Critical issues found in: {', '.join(issue_fields)}")

        return " ".join(summary_parts)
