"""
Field validation utilities for different types of data comparison
"""
import re
from datetime import date, datetime
from typing import Any, Optional
from difflib import Sequence<PERSON>atcher
from loguru import logger

from ..models import ValidationRule, MatchType, FieldType


class FieldValidator:
    """
    Utility class for validating different types of fields
    """
    
    def __init__(self):
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        self.phone_pattern = re.compile(r'^\+?[\d\s\-\(\)]{10,}$')
    
    async def validate_field(
        self,
        expected_value: Any,
        actual_value: Any,
        validation_rule: ValidationRule
    ) -> float:
        """
        Main validation method that routes to specific validators based on field type
        """
        try:
            if expected_value is None and actual_value is None:
                return 1.0
            
            if expected_value is None or actual_value is None:
                return 0.0 if validation_rule.is_required else 0.5
            
            # Route to appropriate validator based on field type
            if validation_rule.field_type == FieldType.TEXT:
                return self._validate_text_field(expected_value, actual_value, validation_rule)
            
            elif validation_rule.field_type == FieldType.DATE:
                return self._validate_date_field(expected_value, actual_value, validation_rule)
            
            elif validation_rule.field_type == FieldType.NUMBER:
                return self._validate_number_field(expected_value, actual_value, validation_rule)
            
            elif validation_rule.field_type == FieldType.EMAIL:
                return self._validate_email_field(expected_value, actual_value, validation_rule)
            
            elif validation_rule.field_type == FieldType.PHONE:
                return self._validate_phone_field(expected_value, actual_value, validation_rule)
            
            elif validation_rule.field_type == FieldType.ADDRESS:
                return self._validate_address_field(expected_value, actual_value, validation_rule)
            
            elif validation_rule.field_type == FieldType.CURRENCY:
                return self._validate_currency_field(expected_value, actual_value, validation_rule)
            
            else:
                logger.warning(f"Unknown field type: {validation_rule.field_type}")
                return self._validate_text_field(expected_value, actual_value, validation_rule)
                
        except Exception as e:
            logger.error(f"Error validating field: {str(e)}")
            return 0.0
    
    def _validate_text_field(
        self,
        expected_value: str,
        actual_value: str,
        validation_rule: ValidationRule
    ) -> float:
        """Validate text fields using various matching strategies"""
        
        if validation_rule.match_type == MatchType.EXACT:
            return 1.0 if expected_value.strip().lower() == actual_value.strip().lower() else 0.0
        
        elif validation_rule.match_type == MatchType.FUZZY:
            similarity = self.calculate_text_similarity(expected_value, actual_value)
            threshold = validation_rule.similarity_threshold or 0.8
            return similarity if similarity >= threshold else 0.0
        
        elif validation_rule.match_type == MatchType.SEMANTIC:
            # For semantic matching, we would use embeddings/NLP
            # For now, fall back to fuzzy matching
            return self._validate_text_field(
                expected_value, 
                actual_value, 
                ValidationRule(
                    field_name=validation_rule.field_name,
                    field_type=validation_rule.field_type,
                    match_type=MatchType.FUZZY,
                    similarity_threshold=validation_rule.similarity_threshold
                )
            )
        
        elif validation_rule.match_type == MatchType.REGEX:
            if validation_rule.regex_pattern:
                pattern = re.compile(validation_rule.regex_pattern, re.IGNORECASE)
                return 1.0 if pattern.match(actual_value) else 0.0
            else:
                return self._validate_text_field(expected_value, actual_value, 
                    ValidationRule(
                        field_name=validation_rule.field_name,
                        field_type=validation_rule.field_type,
                        match_type=MatchType.EXACT
                    )
                )
        
        else:
            # Default to fuzzy matching
            return self.calculate_text_similarity(expected_value, actual_value)
    
    def _validate_date_field(
        self,
        expected_value: date,
        actual_value: date,
        validation_rule: ValidationRule
    ) -> float:
        """Validate date fields with tolerance"""
        
        if isinstance(expected_value, str):
            expected_value = datetime.fromisoformat(expected_value).date()
        if isinstance(actual_value, str):
            actual_value = datetime.fromisoformat(actual_value).date()
        
        if validation_rule.match_type == MatchType.EXACT:
            return 1.0 if expected_value == actual_value else 0.0
        
        elif validation_rule.match_type == MatchType.DATE_TOLERANCE:
            tolerance_days = validation_rule.date_tolerance_days or 30
            diff_days = abs((expected_value - actual_value).days)
            
            if diff_days == 0:
                return 1.0
            elif diff_days <= tolerance_days:
                # Linear decay within tolerance
                return 1.0 - (diff_days / tolerance_days) * 0.3  # Max 30% penalty
            else:
                # Exponential decay beyond tolerance
                excess_days = diff_days - tolerance_days
                penalty = min(0.7, excess_days / tolerance_days * 0.5)
                return max(0.0, 0.7 - penalty)
        
        else:
            # Default to exact match for dates
            return 1.0 if expected_value == actual_value else 0.0
    
    def _validate_number_field(
        self,
        expected_value: float,
        actual_value: float,
        validation_rule: ValidationRule
    ) -> float:
        """Validate numeric fields with tolerance"""
        
        expected_value = float(expected_value)
        actual_value = float(actual_value)
        
        if validation_rule.match_type == MatchType.EXACT:
            return 1.0 if expected_value == actual_value else 0.0
        
        elif validation_rule.match_type == MatchType.RANGE:
            # Check percentage tolerance
            if validation_rule.numeric_tolerance_percent:
                tolerance = expected_value * (validation_rule.numeric_tolerance_percent / 100)
                diff = abs(expected_value - actual_value)
                
                if diff <= tolerance:
                    return 1.0 - (diff / tolerance) * 0.2  # Max 20% penalty within tolerance
                else:
                    # Beyond tolerance
                    excess = diff - tolerance
                    penalty = min(0.8, excess / tolerance * 0.4)
                    return max(0.0, 0.8 - penalty)
            
            # Check absolute tolerance
            elif validation_rule.numeric_tolerance_absolute:
                tolerance = validation_rule.numeric_tolerance_absolute
                diff = abs(expected_value - actual_value)
                
                if diff <= tolerance:
                    return 1.0 - (diff / tolerance) * 0.2
                else:
                    excess = diff - tolerance
                    penalty = min(0.8, excess / tolerance * 0.4)
                    return max(0.0, 0.8 - penalty)
            
            else:
                # No tolerance specified, use exact match
                return 1.0 if expected_value == actual_value else 0.0
        
        else:
            return 1.0 if expected_value == actual_value else 0.0
    
    def _validate_email_field(
        self,
        expected_value: str,
        actual_value: str,
        validation_rule: ValidationRule
    ) -> float:
        """Validate email fields"""
        
        # First check if both are valid emails
        expected_valid = bool(self.email_pattern.match(expected_value))
        actual_valid = bool(self.email_pattern.match(actual_value))
        
        if not expected_valid or not actual_valid:
            return 0.0
        
        # Then compare
        if validation_rule.match_type == MatchType.EXACT:
            return 1.0 if expected_value.lower() == actual_value.lower() else 0.0
        else:
            # For emails, usually exact match is required
            return 1.0 if expected_value.lower() == actual_value.lower() else 0.0
    
    def _validate_phone_field(
        self,
        expected_value: str,
        actual_value: str,
        validation_rule: ValidationRule
    ) -> float:
        """Validate phone number fields"""
        
        # Normalize phone numbers (remove spaces, dashes, parentheses)
        expected_normalized = re.sub(r'[\s\-\(\)]', '', expected_value)
        actual_normalized = re.sub(r'[\s\-\(\)]', '', actual_value)
        
        # Remove country codes for comparison if present
        if expected_normalized.startswith('+'):
            expected_normalized = expected_normalized[1:]
        if actual_normalized.startswith('+'):
            actual_normalized = actual_normalized[1:]
        
        if validation_rule.match_type == MatchType.EXACT:
            return 1.0 if expected_normalized == actual_normalized else 0.0
        else:
            # For phone numbers, check if the core number matches
            # (allowing for different formatting)
            if len(expected_normalized) >= 10 and len(actual_normalized) >= 10:
                # Compare last 10 digits
                expected_core = expected_normalized[-10:]
                actual_core = actual_normalized[-10:]
                return 1.0 if expected_core == actual_core else 0.0
            else:
                return 1.0 if expected_normalized == actual_normalized else 0.0
    
    def _validate_address_field(
        self,
        expected_value: Any,
        actual_value: Any,
        validation_rule: ValidationRule
    ) -> float:
        """Validate address fields"""
        
        # Convert to string if Address objects
        if hasattr(expected_value, '__str__'):
            expected_str = str(expected_value)
        else:
            expected_str = str(expected_value)
        
        if hasattr(actual_value, '__str__'):
            actual_str = str(actual_value)
        else:
            actual_str = str(actual_value)
        
        # Use fuzzy matching for addresses
        return self.calculate_text_similarity(expected_str, actual_str)
    
    def _validate_currency_field(
        self,
        expected_value: float,
        actual_value: float,
        validation_rule: ValidationRule
    ) -> float:
        """Validate currency fields (similar to number but with currency-specific logic)"""
        
        # For currency, we typically allow some tolerance
        return self._validate_number_field(expected_value, actual_value, validation_rule)
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        if not text1 or not text2:
            return 0.0
        
        # Normalize texts
        text1_norm = text1.strip().lower()
        text2_norm = text2.strip().lower()
        
        if text1_norm == text2_norm:
            return 1.0
        
        # Use SequenceMatcher for similarity
        similarity = SequenceMatcher(None, text1_norm, text2_norm).ratio()
        
        return similarity
    
    def is_similar_text(self, text1: str, text2: str, threshold: float = 0.8) -> bool:
        """Check if two texts are similar above a threshold"""
        return self.calculate_text_similarity(text1, text2) >= threshold
