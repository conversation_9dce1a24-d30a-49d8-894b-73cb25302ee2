"""
Pytest configuration and shared fixtures
"""
import pytest
import asyncio
from datetime import date
from unittest.mock import MagicMock, AsyncMock

from src.models import (
    PersonalInfo,
    EmploymentRecord,
    ValidationRule,
    FieldType,
    MatchType
)
from src.models.enriched_cpf import CandidateDeclaredInfo, EnrichedCPF
from src.models.client_input import (
    ClientInput,
    ExpectedPersonalInfo,
    ExpectedEmployment
)
from src.models.validation_matrix import (
    ValidationMatrix,
    EmploymentValidationRules,
    PersonalInfoValidationRules
)
from data.sample_data import (
    create_sample_enriched_cpf,
    create_sample_client_input,
    create_sample_validation_matrix
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def sample_personal_info():
    """Create sample personal information for testing"""
    return PersonalInfo(
        full_name="<PERSON>",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        date_of_birth=date(1990, 1, 1),
        national_id="123456789",
        email="<EMAIL>",
        phone="+1234567890"
    )


@pytest.fixture
def sample_employment_record():
    """Create sample employment record for testing"""
    return EmploymentRecord(
        employer_name="Tech Corp",
        job_title="Software Engineer",
        start_date=date(2020, 1, 1),
        end_date=date(2023, 12, 31),
        salary=75000.0,
        currency="USD"
    )


@pytest.fixture
def sample_validation_rule():
    """Create sample validation rule for testing"""
    return ValidationRule(
        field_name="employer_name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.8,
        priority="high",
        weight=1.0,
        is_required=True,
        green_threshold=0.9,
        amber_threshold=0.7
    )


@pytest.fixture
def sample_enriched_cpf():
    """Create sample enriched CPF for testing"""
    return create_sample_enriched_cpf()


@pytest.fixture
def sample_client_input():
    """Create sample client input for testing"""
    return create_sample_client_input()


@pytest.fixture
def sample_validation_matrix():
    """Create sample validation matrix for testing"""
    return create_sample_validation_matrix()


@pytest.fixture
def mock_verification_engine():
    """Create mock verification engine"""
    mock_engine = MagicMock()
    mock_engine.verify_candidate = AsyncMock()
    return mock_engine


@pytest.fixture
def mock_field_validator():
    """Create mock field validator"""
    mock_validator = MagicMock()
    mock_validator.validate_field = AsyncMock(return_value=0.9)
    mock_validator.is_similar_text = MagicMock(return_value=True)
    mock_validator.calculate_text_similarity = MagicMock(return_value=0.9)
    return mock_validator


@pytest.fixture
def mock_llm_client():
    """Create mock LLM client"""
    mock_client = MagicMock()
    mock_client.analyze_verification = AsyncMock(return_value={
        "overall_assessment": {
            "confidence_score": 0.9,
            "color_code": "Green",
            "summary": "Test analysis"
        },
        "employer_match": {
            "score": 0.9,
            "explanation": "Good match"
        }
    })
    return mock_client


@pytest.fixture
def mock_scorer():
    """Create mock verification scorer"""
    mock_scorer = MagicMock()
    mock_scorer.calculate_field_score = MagicMock(return_value=(0.9, "Green", "Good match"))
    mock_scorer.calculate_overall_score = MagicMock(return_value=(0.9, "Green", False))
    return mock_scorer


# Test data fixtures
@pytest.fixture
def perfect_match_data():
    """Test data for perfect match scenario"""
    from data.sample_data import create_perfect_match_scenario
    return create_perfect_match_scenario()


@pytest.fixture
def discrepancy_data():
    """Test data for discrepancy scenario"""
    from data.sample_data import create_discrepancy_scenario
    return create_discrepancy_scenario()


@pytest.fixture
def major_issues_data():
    """Test data for major issues scenario"""
    from data.sample_data import create_major_issues_scenario
    return create_major_issues_scenario()


# API testing fixtures
@pytest.fixture
def api_client():
    """Create test client for API testing"""
    from fastapi.testclient import TestClient
    from src.api.main import app
    return TestClient(app)


@pytest.fixture
def sample_api_request():
    """Create sample API request data"""
    enriched_cpf = create_sample_enriched_cpf()
    client_input = create_sample_client_input()
    validation_matrix = create_sample_validation_matrix()
    
    return {
        "enriched_cpf": enriched_cpf.dict(),
        "client_input": client_input.dict(),
        "validation_matrix": validation_matrix.dict()
    }


# Database fixtures (for future use)
@pytest.fixture
def mock_database():
    """Create mock database connection"""
    mock_db = MagicMock()
    mock_db.execute = AsyncMock()
    mock_db.fetch = AsyncMock(return_value=[])
    mock_db.fetchrow = AsyncMock(return_value=None)
    return mock_db


# Configuration fixtures
@pytest.fixture
def test_settings():
    """Create test settings"""
    from config.settings import Settings
    
    return Settings(
        api_host="localhost",
        api_port=8000,
        openai_api_key="test-key",
        log_level="DEBUG",
        confidence_threshold_green=0.9,
        confidence_threshold_amber=0.7,
        human_review_threshold=0.6
    )


# Utility fixtures
@pytest.fixture
def temp_directory(tmp_path):
    """Create temporary directory for testing"""
    return tmp_path


@pytest.fixture
def sample_file_content():
    """Sample file content for testing"""
    return """
    {
        "test_data": {
            "candidate_id": "TEST_001",
            "name": "Test Candidate",
            "score": 0.85
        }
    }
    """


# Parametrized fixtures for different test scenarios
@pytest.fixture(params=[
    {"similarity": 1.0, "expected_color": "Green"},
    {"similarity": 0.8, "expected_color": "Amber"},
    {"similarity": 0.5, "expected_color": "Red"}
])
def similarity_test_case(request):
    """Parametrized fixture for similarity testing"""
    return request.param


@pytest.fixture(params=[
    {"tolerance_days": 0, "expected_score": 1.0},
    {"tolerance_days": 15, "expected_score": 0.85},
    {"tolerance_days": 45, "expected_score": 0.5}
])
def date_tolerance_test_case(request):
    """Parametrized fixture for date tolerance testing"""
    return request.param


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Cleanup after each test"""
    yield
    # Cleanup code here if needed
    pass


# Markers for different test categories
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "api: mark test as an API test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "requires_llm: mark test as requiring LLM access"
    )


# Skip conditions
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add skip conditions"""
    import os
    
    # Skip LLM tests if no API key is provided
    if not os.getenv("OPENAI_API_KEY"):
        skip_llm = pytest.mark.skip(reason="No OpenAI API key provided")
        for item in items:
            if "requires_llm" in item.keywords:
                item.add_marker(skip_llm)


# Custom assertions
class CustomAssertions:
    """Custom assertion helpers"""
    
    @staticmethod
    def assert_color_code_valid(color_code):
        """Assert that color code is valid"""
        valid_colors = ["Green", "Amber", "Red"]
        assert color_code in valid_colors, f"Invalid color code: {color_code}"
    
    @staticmethod
    def assert_confidence_score_valid(score):
        """Assert that confidence score is valid"""
        assert 0.0 <= score <= 1.0, f"Invalid confidence score: {score}"
    
    @staticmethod
    def assert_verification_result_valid(result):
        """Assert that verification result is valid"""
        assert hasattr(result, 'field_name')
        assert hasattr(result, 'color_code')
        assert hasattr(result, 'confidence_score')
        assert hasattr(result, 'remark')
        
        CustomAssertions.assert_color_code_valid(result.color_code.value)
        CustomAssertions.assert_confidence_score_valid(result.confidence_score)


@pytest.fixture
def custom_assertions():
    """Provide custom assertions"""
    return CustomAssertions
