"""
Unit tests for data models
"""
import pytest
from datetime import date, datetime
from pydantic import ValidationError

from src.models import (
    PersonalInfo,
    Address,
    EmploymentRecord,
    EducationRecord,
    EnrichedCPF,
    ClientInput,
    ValidationMatrix,
    VerificationResult,
    OverallVerificationResult,
    ColorCode,
    VerificationStatus,
    ValidationRule,
    MatchType,
    FieldType
)


class TestPersonalInfo:
    """Test PersonalInfo model"""
    
    def test_valid_personal_info(self):
        """Test creating valid personal info"""
        info = PersonalInfo(
            full_name="<PERSON>",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            date_of_birth=date(1990, 1, 1),
            national_id="123456789",
            email="<EMAIL>",
            phone="+1234567890"
        )
        
        assert info.full_name == "<PERSON>"
        assert info.first_name == "<PERSON>"
        assert info.last_name == "<PERSON><PERSON>"
        assert info.date_of_birth == date(1990, 1, 1)
        assert info.national_id == "123456789"
        assert info.email == "<EMAIL>"
        assert info.phone == "+1234567890"
    
    def test_invalid_email(self):
        """Test validation of invalid email"""
        with pytest.raises(ValidationError):
            PersonalInfo(
                full_name="John Doe",
                email="invalid-email"
            )
    
    def test_minimal_personal_info(self):
        """Test creating personal info with minimal required fields"""
        info = PersonalInfo(full_name="John Doe")
        assert info.full_name == "<PERSON>"
        assert info.first_name is None
        assert info.email is None


class TestAddress:
    """Test Address model"""
    
    def test_valid_address(self):
        """Test creating valid address"""
        address = Address(
            street="123 Main St",
            city="New York",
            state="NY",
            country="USA",
            postal_code="10001"
        )
        
        assert address.street == "123 Main St"
        assert address.city == "New York"
        assert address.state == "NY"
        assert address.country == "USA"
        assert address.postal_code == "10001"
    
    def test_address_string_representation(self):
        """Test address string representation"""
        address = Address(
            street="123 Main St",
            city="New York",
            state="NY",
            country="USA",
            postal_code="10001"
        )
        
        expected = "123 Main St, New York, NY, USA, 10001"
        assert str(address) == expected
    
    def test_minimal_address(self):
        """Test creating address with minimal required fields"""
        address = Address(city="New York", country="USA")
        assert address.city == "New York"
        assert address.country == "USA"
        assert address.street is None


class TestEmploymentRecord:
    """Test EmploymentRecord model"""
    
    def test_valid_employment_record(self):
        """Test creating valid employment record"""
        employment = EmploymentRecord(
            employer_name="Tech Corp",
            job_title="Software Engineer",
            start_date=date(2020, 1, 1),
            end_date=date(2023, 12, 31),
            salary=75000.0,
            currency="USD"
        )
        
        assert employment.employer_name == "Tech Corp"
        assert employment.job_title == "Software Engineer"
        assert employment.start_date == date(2020, 1, 1)
        assert employment.end_date == date(2023, 12, 31)
        assert employment.salary == 75000.0
        assert employment.currency == "USD"
    
    def test_invalid_end_date(self):
        """Test validation of invalid end date"""
        with pytest.raises(ValidationError):
            EmploymentRecord(
                employer_name="Tech Corp",
                job_title="Software Engineer",
                start_date=date(2020, 1, 1),
                end_date=date(2019, 12, 31)  # Before start date
            )
    
    def test_negative_salary(self):
        """Test validation of negative salary"""
        with pytest.raises(ValidationError):
            EmploymentRecord(
                employer_name="Tech Corp",
                job_title="Software Engineer",
                start_date=date(2020, 1, 1),
                salary=-1000.0
            )
    
    def test_current_employment(self):
        """Test current employment (no end date)"""
        employment = EmploymentRecord(
            employer_name="Tech Corp",
            job_title="Software Engineer",
            start_date=date(2020, 1, 1)
        )
        
        assert employment.end_date is None


class TestValidationRule:
    """Test ValidationRule model"""
    
    def test_valid_validation_rule(self):
        """Test creating valid validation rule"""
        rule = ValidationRule(
            field_name="employer_name",
            field_type=FieldType.TEXT,
            match_type=MatchType.FUZZY,
            similarity_threshold=0.8,
            priority="high",
            weight=1.0,
            is_required=True
        )
        
        assert rule.field_name == "employer_name"
        assert rule.field_type == FieldType.TEXT
        assert rule.match_type == MatchType.FUZZY
        assert rule.similarity_threshold == 0.8
        assert rule.priority == "high"
        assert rule.weight == 1.0
        assert rule.is_required is True
    
    def test_invalid_thresholds(self):
        """Test validation of invalid thresholds"""
        with pytest.raises(ValidationError):
            ValidationRule(
                field_name="test_field",
                field_type=FieldType.TEXT,
                match_type=MatchType.FUZZY,
                green_threshold=0.7,
                amber_threshold=0.8  # Should be less than green
            )


class TestVerificationResult:
    """Test VerificationResult model"""
    
    def test_valid_verification_result(self):
        """Test creating valid verification result"""
        result = VerificationResult(
            field_name="employer_name",
            color_code=ColorCode.GREEN,
            remark="Employer name matches exactly",
            confidence_score=1.0,
            requires_human_review=False,
            expected_value="Tech Corp",
            actual_value="Tech Corp"
        )
        
        assert result.field_name == "employer_name"
        assert result.color_code == ColorCode.GREEN
        assert result.remark == "Employer name matches exactly"
        assert result.confidence_score == 1.0
        assert result.requires_human_review is False
        assert result.expected_value == "Tech Corp"
        assert result.actual_value == "Tech Corp"


class TestOverallVerificationResult:
    """Test OverallVerificationResult model"""
    
    def test_valid_overall_result(self):
        """Test creating valid overall verification result"""
        result = OverallVerificationResult(
            candidate_id="CAND_12345",
            status=VerificationStatus.COMPLETED
        )
        
        assert result.candidate_id == "CAND_12345"
        assert result.status == VerificationStatus.COMPLETED
        assert result.field_results == []
        assert result.overall_score is None
    
    def test_add_field_result(self):
        """Test adding field results"""
        overall_result = OverallVerificationResult(
            candidate_id="CAND_12345",
            status=VerificationStatus.COMPLETED
        )
        
        field_result = VerificationResult(
            field_name="employer_name",
            color_code=ColorCode.GREEN,
            remark="Match",
            confidence_score=1.0
        )
        
        overall_result.add_field_result(field_result)
        assert len(overall_result.field_results) == 1
        assert overall_result.field_results[0] == field_result
    
    def test_calculate_overall_score(self):
        """Test calculating overall score"""
        overall_result = OverallVerificationResult(
            candidate_id="CAND_12345",
            status=VerificationStatus.COMPLETED
        )
        
        # Add some field results
        field_results = [
            VerificationResult(
                field_name="employer_name",
                color_code=ColorCode.GREEN,
                remark="Match",
                confidence_score=1.0
            ),
            VerificationResult(
                field_name="start_date",
                color_code=ColorCode.AMBER,
                remark="Close match",
                confidence_score=0.8
            ),
            VerificationResult(
                field_name="salary",
                color_code=ColorCode.RED,
                remark="No match",
                confidence_score=0.3
            )
        ]
        
        for result in field_results:
            overall_result.add_field_result(result)
        
        # Calculate overall score
        score = overall_result.calculate_overall_score()
        
        # Should be average: (1.0 + 0.8 + 0.3) / 3 = 0.7
        assert abs(score - 0.7) < 0.01
        assert overall_result.overall_score == score
        assert overall_result.overall_color_code == ColorCode.AMBER
    
    def test_calculate_weighted_score(self):
        """Test calculating weighted overall score"""
        overall_result = OverallVerificationResult(
            candidate_id="CAND_12345",
            status=VerificationStatus.COMPLETED
        )
        
        # Add field results
        field_results = [
            VerificationResult(
                field_name="employer_name",
                color_code=ColorCode.GREEN,
                remark="Match",
                confidence_score=1.0
            ),
            VerificationResult(
                field_name="salary",
                color_code=ColorCode.AMBER,
                remark="Close match",
                confidence_score=0.8
            )
        ]
        
        for result in field_results:
            overall_result.add_field_result(result)
        
        # Use custom weights
        weights = {
            "employer_name": 0.7,
            "salary": 0.3
        }
        
        score = overall_result.calculate_overall_score(weights)
        
        # Should be weighted: (1.0 * 0.7 + 0.8 * 0.3) / 1.0 = 0.94
        assert abs(score - 0.94) < 0.01


class TestEnrichedCPF:
    """Test EnrichedCPF model"""
    
    def test_get_current_employment(self):
        """Test getting current employment"""
        from src.models.enriched_cpf import CandidateDeclaredInfo
        
        # Create employment records
        past_employment = EmploymentRecord(
            employer_name="Old Corp",
            job_title="Junior Developer",
            start_date=date(2018, 1, 1),
            end_date=date(2020, 12, 31)
        )
        
        current_employment = EmploymentRecord(
            employer_name="Current Corp",
            job_title="Senior Developer",
            start_date=date(2021, 1, 1)
            # No end_date = current
        )
        
        declared_info = CandidateDeclaredInfo(
            personal_info=PersonalInfo(full_name="John Doe"),
            employment_history=[past_employment, current_employment]
        )
        
        cpf = EnrichedCPF(
            candidate_id="CAND_12345",
            declared_info=declared_info
        )
        
        current = cpf.get_current_employment()
        assert current is not None
        assert current.employer_name == "Current Corp"
        assert current.end_date is None
    
    def test_get_total_experience_years(self):
        """Test calculating total experience years"""
        from src.models.enriched_cpf import CandidateDeclaredInfo
        
        # Create employment records (2 years + 1 year = 3 years total)
        employment1 = EmploymentRecord(
            employer_name="Corp 1",
            job_title="Developer",
            start_date=date(2020, 1, 1),
            end_date=date(2022, 1, 1)  # 2 years
        )
        
        employment2 = EmploymentRecord(
            employer_name="Corp 2",
            job_title="Senior Developer",
            start_date=date(2022, 1, 1),
            end_date=date(2023, 1, 1)  # 1 year
        )
        
        declared_info = CandidateDeclaredInfo(
            personal_info=PersonalInfo(full_name="John Doe"),
            employment_history=[employment1, employment2]
        )
        
        cpf = EnrichedCPF(
            candidate_id="CAND_12345",
            declared_info=declared_info
        )
        
        experience = cpf.get_total_experience_years()
        assert abs(experience - 3.0) < 0.1  # Allow small floating point differences
