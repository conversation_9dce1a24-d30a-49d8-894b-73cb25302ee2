"""
Unit tests for verification engine
"""
import pytest
from datetime import date
from unittest.mock import AsyncMock, MagicMock

from src.verification import Veri<PERSON>Engine, FieldValidator
from src.models import (
    EnrichedCPF,
    ClientInput,
    ValidationMatrix,
    PersonalInfo,
    EmploymentRecord,
    VerificationStatus,
    ColorCode,
    ValidationRule,
    MatchType,
    FieldType
)
from src.models.enriched_cpf import CandidateDeclaredInfo
from src.models.client_input import ExpectedPersonalInfo, ExpectedEmployment
from src.models.validation_matrix import (
    EmploymentValidationRules,
    PersonalInfoValidationRules
)


@pytest.fixture
def sample_enriched_cpf():
    """Create sample enriched CPF for testing"""
    personal_info = PersonalInfo(
        full_name="<PERSON>",
        email="<EMAIL>",
        phone="+1234567890",
        national_id="123456789"
    )
    
    employment = EmploymentRecord(
        employer_name="Tech Corp",
        job_title="Software Engineer",
        start_date=date(2020, 1, 1),
        end_date=date(2023, 12, 31),
        salary=75000.0,
        currency="USD"
    )
    
    declared_info = CandidateDeclaredInfo(
        personal_info=personal_info,
        employment_history=[employment]
    )
    
    return EnrichedCPF(
        candidate_id="CAND_12345",
        declared_info=declared_info
    )


@pytest.fixture
def sample_client_input():
    """Create sample client input for testing"""
    expected_personal_info = ExpectedPersonalInfo(
        full_name="John Doe",
        email="<EMAIL>",
        national_id="123456789"
    )
    
    expected_employment = ExpectedEmployment(
        employer_name="Tech Corp",
        start_date=date(2020, 1, 1),
        min_salary=70000.0,
        max_salary=80000.0
    )
    
    return ClientInput(
        client_id="CLIENT_001",
        candidate_id="CAND_12345",
        expected_personal_info=expected_personal_info,
        expected_employment=[expected_employment]
    )


@pytest.fixture
def sample_validation_matrix():
    """Create sample validation matrix for testing"""
    # Create validation rules
    employer_name_rule = ValidationRule(
        field_name="employer_name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.8,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    start_date_rule = ValidationRule(
        field_name="start_date",
        field_type=FieldType.DATE,
        match_type=MatchType.DATE_TOLERANCE,
        date_tolerance_days=30,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    name_rule = ValidationRule(
        field_name="name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.8,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    employment_rules = EmploymentValidationRules(
        employer_name_rule=employer_name_rule,
        start_date_rule=start_date_rule
    )
    
    personal_info_rules = PersonalInfoValidationRules(
        name_rule=name_rule
    )
    
    return ValidationMatrix(
        company_id="CLIENT_001",
        matrix_name="Test Matrix",
        employment_rules=employment_rules,
        personal_info_rules=personal_info_rules
    )


class TestVerificationEngine:
    """Test VerificationEngine class"""
    
    @pytest.mark.asyncio
    async def test_verify_candidate_success(
        self,
        sample_enriched_cpf,
        sample_client_input,
        sample_validation_matrix
    ):
        """Test successful candidate verification"""
        engine = VerificationEngine()
        
        # Mock the field validator
        engine.field_validator = MagicMock()
        engine.field_validator.validate_field = AsyncMock(return_value=0.95)
        engine.field_validator.is_similar_text = MagicMock(return_value=True)
        
        result = await engine.verify_candidate(
            sample_enriched_cpf,
            sample_client_input,
            sample_validation_matrix
        )
        
        assert result.candidate_id == "CAND_12345"
        assert result.status == VerificationStatus.COMPLETED
        assert result.overall_score is not None
        assert result.overall_color_code is not None
        assert len(result.field_results) > 0
        assert result.processing_time_seconds is not None
    
    @pytest.mark.asyncio
    async def test_verify_employment_exact_match(
        self,
        sample_enriched_cpf,
        sample_client_input,
        sample_validation_matrix
    ):
        """Test employment verification with exact match"""
        engine = VerificationEngine()
        
        # Mock field validator to return perfect scores
        engine.field_validator = MagicMock()
        engine.field_validator.validate_field = AsyncMock(return_value=1.0)
        engine.field_validator.is_similar_text = MagicMock(return_value=True)
        
        employment_results = await engine._verify_employment(
            sample_enriched_cpf,
            sample_client_input,
            sample_validation_matrix
        )
        
        assert len(employment_results) > 0
        
        # Check that we have employer name verification
        employer_results = [r for r in employment_results if "employer_name" in r.field_name]
        assert len(employer_results) > 0
        
        employer_result = employer_results[0]
        assert employer_result.color_code == ColorCode.GREEN
        assert employer_result.confidence_score == 1.0
    
    @pytest.mark.asyncio
    async def test_verify_employment_not_found(
        self,
        sample_enriched_cpf,
        sample_client_input,
        sample_validation_matrix
    ):
        """Test employment verification when employment not found"""
        engine = VerificationEngine()
        
        # Mock field validator
        engine.field_validator = MagicMock()
        engine.field_validator.is_similar_text = MagicMock(return_value=False)
        
        # Modify client input to expect different employer
        sample_client_input.expected_employment[0].employer_name = "Different Corp"
        
        employment_results = await engine._verify_employment(
            sample_enriched_cpf,
            sample_client_input,
            sample_validation_matrix
        )
        
        assert len(employment_results) > 0
        
        # Should have a result indicating employment not found
        not_found_results = [r for r in employment_results if r.color_code == ColorCode.RED]
        assert len(not_found_results) > 0
        
        not_found_result = not_found_results[0]
        assert not_found_result.confidence_score == 0.0
        assert not_found_result.requires_human_review is True
        assert "not found" in not_found_result.remark.lower()
    
    @pytest.mark.asyncio
    async def test_verify_personal_info(
        self,
        sample_enriched_cpf,
        sample_client_input,
        sample_validation_matrix
    ):
        """Test personal information verification"""
        engine = VerificationEngine()
        
        # Mock field validator
        engine.field_validator = MagicMock()
        engine.field_validator.validate_field = AsyncMock(return_value=0.95)
        
        personal_results = await engine._verify_personal_info(
            sample_enriched_cpf,
            sample_client_input,
            sample_validation_matrix
        )
        
        assert len(personal_results) > 0
        
        # Check that we have name verification
        name_results = [r for r in personal_results if "name" in r.field_name]
        assert len(name_results) > 0
        
        name_result = name_results[0]
        assert name_result.confidence_score == 0.95
        assert name_result.color_code == ColorCode.GREEN
    
    @pytest.mark.asyncio
    async def test_verify_salary_in_range(
        self,
        sample_enriched_cpf,
        sample_client_input,
        sample_validation_matrix
    ):
        """Test salary verification when salary is in expected range"""
        engine = VerificationEngine()
        
        expected_emp = sample_client_input.expected_employment[0]
        actual_emp = sample_enriched_cpf.declared_info.employment_history[0]
        
        salary_result = await engine._verify_salary(
            expected_emp,
            actual_emp,
            None  # No specific validation rule
        )
        
        assert salary_result.field_name == "salary"
        assert salary_result.color_code == ColorCode.GREEN
        assert salary_result.confidence_score == 1.0
        assert "within expected range" in salary_result.remark
    
    @pytest.mark.asyncio
    async def test_verify_salary_out_of_range(
        self,
        sample_enriched_cpf,
        sample_client_input,
        sample_validation_matrix
    ):
        """Test salary verification when salary is out of expected range"""
        engine = VerificationEngine()
        
        # Modify expected salary range to be much lower
        expected_emp = sample_client_input.expected_employment[0]
        expected_emp.min_salary = 50000.0
        expected_emp.max_salary = 60000.0
        
        actual_emp = sample_enriched_cpf.declared_info.employment_history[0]
        # Actual salary is 75000, which is above the range
        
        salary_result = await engine._verify_salary(
            expected_emp,
            actual_emp,
            None
        )
        
        assert salary_result.field_name == "salary"
        assert salary_result.color_code in [ColorCode.AMBER, ColorCode.RED]
        assert salary_result.confidence_score < 1.0
        assert "outside range" in salary_result.remark
    
    @pytest.mark.asyncio
    async def test_verify_salary_missing(
        self,
        sample_enriched_cpf,
        sample_client_input,
        sample_validation_matrix
    ):
        """Test salary verification when salary is missing"""
        engine = VerificationEngine()
        
        expected_emp = sample_client_input.expected_employment[0]
        actual_emp = sample_enriched_cpf.declared_info.employment_history[0]
        
        # Remove salary from actual employment
        actual_emp.salary = None
        
        salary_result = await engine._verify_salary(
            expected_emp,
            actual_emp,
            None
        )
        
        assert salary_result.field_name == "salary"
        assert salary_result.color_code == ColorCode.RED
        assert salary_result.confidence_score == 0.0
        assert salary_result.requires_human_review is True
        assert "not provided" in salary_result.remark
    
    def test_find_matching_employment(self, sample_enriched_cpf):
        """Test finding matching employment record"""
        engine = VerificationEngine()
        
        # Mock field validator
        engine.field_validator = MagicMock()
        engine.field_validator.is_similar_text = MagicMock(return_value=True)
        
        # Create expected employment
        from src.models.client_input import ExpectedEmployment
        expected_emp = ExpectedEmployment(
            employer_name="Tech Corp",
            job_title="Software Engineer"
        )
        
        actual_employment_list = sample_enriched_cpf.declared_info.employment_history
        
        matching_emp = engine._find_matching_employment(
            expected_emp,
            actual_employment_list
        )
        
        assert matching_emp is not None
        assert matching_emp.employer_name == "Tech Corp"
        
        # Verify the similarity check was called
        engine.field_validator.is_similar_text.assert_called_once()
    
    def test_find_matching_employment_not_found(self, sample_enriched_cpf):
        """Test finding matching employment when no match exists"""
        engine = VerificationEngine()
        
        # Mock field validator to return False
        engine.field_validator = MagicMock()
        engine.field_validator.is_similar_text = MagicMock(return_value=False)
        
        # Create expected employment with different name
        from src.models.client_input import ExpectedEmployment
        expected_emp = ExpectedEmployment(
            employer_name="Different Corp",
            job_title="Software Engineer"
        )
        
        actual_employment_list = sample_enriched_cpf.declared_info.employment_history
        
        matching_emp = engine._find_matching_employment(
            expected_emp,
            actual_employment_list
        )
        
        assert matching_emp is None
    
    def test_generate_summary(self, sample_validation_matrix):
        """Test generating verification summary"""
        engine = VerificationEngine()
        
        # Create mock overall result
        from src.models import OverallVerificationResult, VerificationResult
        
        result = OverallVerificationResult(
            candidate_id="CAND_12345",
            status=VerificationStatus.COMPLETED,
            overall_score=0.85,
            overall_color_code=ColorCode.AMBER
        )
        
        # Add some field results
        field_results = [
            VerificationResult(
                field_name="employer_name",
                color_code=ColorCode.GREEN,
                remark="Match",
                confidence_score=1.0
            ),
            VerificationResult(
                field_name="start_date",
                color_code=ColorCode.AMBER,
                remark="Close match",
                confidence_score=0.8
            ),
            VerificationResult(
                field_name="salary",
                color_code=ColorCode.RED,
                remark="Out of range",
                confidence_score=0.3,
                requires_human_review=True
            )
        ]
        
        for field_result in field_results:
            result.add_field_result(field_result)
        
        summary = engine._generate_summary(result, sample_validation_matrix)
        
        assert "3 fields" in summary
        assert "1 Green, 1 Amber, 1 Red" in summary
        assert "0.85" in summary
        assert "Amber" in summary
        assert "Human review recommended" in summary
        assert "salary" in summary  # Critical issue mentioned
