"""
Integration tests for the API
"""
import pytest
from datetime import date
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from src.api.main import app
from src.models import (
    PersonalInfo,
    EmploymentRecord,
    VerificationStatus,
    ColorCode,
    OverallVerificationResult,
    VerificationResult
)
from src.models.enriched_cpf import CandidateDeclaredInfo, EnrichedCPF
from src.models.client_input import (
    ClientInput,
    ExpectedPersonalInfo,
    ExpectedEmployment
)
from src.models.validation_matrix import (
    ValidationMatrix,
    EmploymentValidationRules,
    PersonalInfoValidationRules,
    ValidationRule,
    FieldType,
    MatchType
)


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def sample_verification_request():
    """Create sample verification request data"""
    # Create personal info
    personal_info = PersonalInfo(
        full_name="John Doe",
        email="<EMAIL>",
        national_id="123456789"
    )
    
    # Create employment record
    employment = EmploymentRecord(
        employer_name="Tech Corp",
        job_title="Software Engineer",
        start_date=date(2020, 1, 1),
        end_date=date(2023, 12, 31),
        salary=75000.0
    )
    
    # Create enriched CPF
    declared_info = CandidateDeclaredInfo(
        personal_info=personal_info,
        employment_history=[employment]
    )
    
    enriched_cpf = EnrichedCPF(
        candidate_id="CAND_12345",
        declared_info=declared_info
    )
    
    # Create expected info
    expected_personal_info = ExpectedPersonalInfo(
        full_name="John Doe",
        email="<EMAIL>",
        national_id="123456789"
    )
    
    expected_employment = ExpectedEmployment(
        employer_name="Tech Corp",
        min_salary=70000.0,
        max_salary=80000.0
    )
    
    client_input = ClientInput(
        client_id="CLIENT_001",
        candidate_id="CAND_12345",
        expected_personal_info=expected_personal_info,
        expected_employment=[expected_employment]
    )
    
    # Create validation rules
    employer_rule = ValidationRule(
        field_name="employer_name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.8
    )
    
    name_rule = ValidationRule(
        field_name="name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.8
    )
    
    employment_rules = EmploymentValidationRules(
        employer_name_rule=employer_rule,
        start_date_rule=employer_rule  # Reusing for simplicity
    )
    
    personal_info_rules = PersonalInfoValidationRules(
        name_rule=name_rule
    )
    
    validation_matrix = ValidationMatrix(
        company_id="CLIENT_001",
        matrix_name="Test Matrix",
        employment_rules=employment_rules,
        personal_info_rules=personal_info_rules
    )
    
    return {
        "enriched_cpf": enriched_cpf.dict(),
        "client_input": client_input.dict(),
        "validation_matrix": validation_matrix.dict()
    }


class TestHealthEndpoint:
    """Test health check endpoint"""
    
    def test_health_check(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
        assert "services" in data
        assert data["services"]["verification_engine"] == "operational"
        assert data["services"]["scoring_system"] == "operational"
        assert data["services"]["api"] == "operational"


class TestVerificationEndpoint:
    """Test verification submission endpoint"""
    
    @patch('src.api.main.verification_engine.verify_candidate')
    def test_submit_verification_success(
        self,
        mock_verify,
        client,
        sample_verification_request
    ):
        """Test successful verification submission"""
        # Mock the verification engine
        mock_result = OverallVerificationResult(
            candidate_id="CAND_12345",
            status=VerificationStatus.COMPLETED,
            overall_score=0.95,
            overall_color_code=ColorCode.GREEN
        )
        mock_verify.return_value = mock_result
        
        response = client.post("/verify", json=sample_verification_request)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "job_id" in data
        assert data["status"] == "pending"
        assert "message" in data
        assert "estimated_completion_time" in data
    
    def test_submit_verification_invalid_data(self, client):
        """Test verification submission with invalid data"""
        invalid_request = {
            "enriched_cpf": {},  # Missing required fields
            "client_input": {},
            "validation_matrix": {}
        }
        
        response = client.post("/verify", json=invalid_request)
        
        assert response.status_code == 422  # Validation error
    
    def test_submit_verification_missing_fields(self, client):
        """Test verification submission with missing fields"""
        incomplete_request = {
            "enriched_cpf": {
                "candidate_id": "CAND_12345"
                # Missing declared_info
            }
        }
        
        response = client.post("/verify", json=incomplete_request)
        
        assert response.status_code == 422


class TestStatusEndpoint:
    """Test status check endpoint"""
    
    @patch('src.api.main.verification_engine.verify_candidate')
    def test_get_status_pending(
        self,
        mock_verify,
        client,
        sample_verification_request
    ):
        """Test getting status of pending job"""
        # Submit a verification request first
        response = client.post("/verify", json=sample_verification_request)
        assert response.status_code == 200
        
        job_id = response.json()["job_id"]
        
        # Check status
        status_response = client.get(f"/status/{job_id}")
        
        assert status_response.status_code == 200
        status_data = status_response.json()
        
        assert status_data["job_id"] == job_id
        assert status_data["status"] in ["pending", "in_progress"]
        assert "created_at" in status_data
        assert "progress_percentage" in status_data
        assert "message" in status_data
    
    def test_get_status_not_found(self, client):
        """Test getting status of non-existent job"""
        response = client.get("/status/non-existent-job-id")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()


class TestResultsEndpoint:
    """Test results retrieval endpoint"""
    
    @patch('src.api.main.verification_engine.verify_candidate')
    def test_get_results_not_ready(
        self,
        mock_verify,
        client,
        sample_verification_request
    ):
        """Test getting results when verification is not complete"""
        # Submit a verification request
        response = client.post("/verify", json=sample_verification_request)
        job_id = response.json()["job_id"]
        
        # Try to get results immediately
        results_response = client.get(f"/results/{job_id}")
        
        assert results_response.status_code == 202  # Accepted, but not ready
        data = results_response.json()
        assert "still in progress" in data["detail"].lower()
    
    def test_get_results_not_found(self, client):
        """Test getting results for non-existent job"""
        response = client.get("/results/non-existent-job-id")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()


class TestJobCancellation:
    """Test job cancellation endpoint"""
    
    @patch('src.api.main.verification_engine.verify_candidate')
    def test_cancel_job_success(
        self,
        mock_verify,
        client,
        sample_verification_request
    ):
        """Test successful job cancellation"""
        # Submit a verification request
        response = client.post("/verify", json=sample_verification_request)
        job_id = response.json()["job_id"]
        
        # Cancel the job
        cancel_response = client.delete(f"/jobs/{job_id}")
        
        assert cancel_response.status_code == 200
        data = cancel_response.json()
        assert "cancelled successfully" in data["message"]
    
    def test_cancel_job_not_found(self, client):
        """Test cancelling non-existent job"""
        response = client.delete("/jobs/non-existent-job-id")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()


class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def test_rate_limiting(self, client):
        """Test that rate limiting works"""
        # This test would need to be adjusted based on actual rate limits
        # For now, just test that the endpoint responds normally
        response = client.get("/health")
        assert response.status_code == 200
        
        # Check for rate limit headers
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert "X-RateLimit-Reset" in response.headers


class TestCORS:
    """Test CORS functionality"""
    
    def test_cors_headers(self, client):
        """Test that CORS headers are present"""
        response = client.options("/health")
        
        # FastAPI automatically handles OPTIONS requests
        assert response.status_code == 200


class TestErrorHandling:
    """Test error handling"""
    
    def test_404_error(self, client):
        """Test 404 error handling"""
        response = client.get("/non-existent-endpoint")
        
        assert response.status_code == 404
    
    def test_method_not_allowed(self, client):
        """Test method not allowed error"""
        response = client.put("/health")  # PUT not allowed on health endpoint
        
        assert response.status_code == 405


class TestRequestLogging:
    """Test request logging functionality"""
    
    def test_request_id_header(self, client):
        """Test that request ID header is added"""
        response = client.get("/health")
        
        assert response.status_code == 200
        assert "X-Request-ID" in response.headers
        assert "X-Process-Time" in response.headers
    
    def test_request_id_unique(self, client):
        """Test that request IDs are unique"""
        response1 = client.get("/health")
        response2 = client.get("/health")
        
        assert response1.headers["X-Request-ID"] != response2.headers["X-Request-ID"]


@pytest.mark.asyncio
class TestAsyncEndpoints:
    """Test async functionality of endpoints"""
    
    async def test_concurrent_requests(self, client):
        """Test handling concurrent requests"""
        import asyncio
        
        async def make_request():
            return client.get("/health")
        
        # Make multiple concurrent requests
        tasks = [make_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
