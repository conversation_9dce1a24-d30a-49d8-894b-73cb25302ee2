"""
Sample data for testing the verification system
"""
from datetime import date
from typing import Dict, Any, List

from src.models import (
    PersonalInfo,
    Address,
    EmploymentRecord,
    EducationRecord,
    ValidationRule,
    FieldType,
    MatchType
)
from src.models.enriched_cpf import (
    EnrichedCPF,
    CandidateDeclaredInfo,
    GovernmentVerification
)
from src.models.client_input import (
    ClientInput,
    ExpectedPersonalInfo,
    ExpectedEmployment,
    ExpectedEducation,
    VerificationRequirements
)
from src.models.validation_matrix import (
    ValidationMatrix,
    EmploymentValidationRules,
    EducationValidationRules,
    PersonalInfoValidationRules,
    ComplianceRules
)


def create_sample_personal_info() -> PersonalInfo:
    """Create sample personal information"""
    return PersonalInfo(
        full_name="<PERSON>",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        date_of_birth=date(1990, 5, 15),
        national_id="***********",
        passport_number="P123456789",
        email="<EMAIL>",
        phone="******-123-4567"
    )


def create_sample_address() -> Address:
    """Create sample address"""
    return Address(
        street="123 Main Street, Apt 4B",
        city="New York",
        state="NY",
        country="USA",
        postal_code="10001"
    )


def create_sample_employment_history() -> List[EmploymentRecord]:
    """Create sample employment history"""
    return [
        EmploymentRecord(
            employer_name="Tech Innovations Inc.",
            job_title="Senior Software Engineer",
            start_date=date(2021, 3, 1),
            end_date=None,  # Current job
            salary=95000.0,
            currency="USD",
            location=Address(
                city="San Francisco",
                state="CA",
                country="USA"
            ),
            employment_type="Full-time"
        ),
        EmploymentRecord(
            employer_name="Digital Solutions Corp",
            job_title="Software Engineer",
            start_date=date(2019, 6, 15),
            end_date=date(2021, 2, 28),
            salary=75000.0,
            currency="USD",
            location=Address(
                city="Austin",
                state="TX",
                country="USA"
            ),
            employment_type="Full-time"
        ),
        EmploymentRecord(
            employer_name="StartupXYZ",
            job_title="Junior Developer",
            start_date=date(2018, 1, 10),
            end_date=date(2019, 6, 10),
            salary=55000.0,
            currency="USD",
            location=Address(
                city="Boston",
                state="MA",
                country="USA"
            ),
            employment_type="Full-time"
        )
    ]


def create_sample_education_history() -> List[EducationRecord]:
    """Create sample education history"""
    return [
        EducationRecord(
            institution_name="Massachusetts Institute of Technology",
            degree="Master of Science",
            field_of_study="Computer Science",
            start_date=date(2016, 9, 1),
            end_date=date(2018, 5, 31),
            grade="3.8 GPA",
            location=Address(
                city="Cambridge",
                state="MA",
                country="USA"
            )
        ),
        EducationRecord(
            institution_name="University of California, Berkeley",
            degree="Bachelor of Science",
            field_of_study="Computer Science",
            start_date=date(2012, 8, 15),
            end_date=date(2016, 5, 20),
            grade="3.6 GPA",
            location=Address(
                city="Berkeley",
                state="CA",
                country="USA"
            )
        )
    ]


def create_sample_enriched_cpf() -> EnrichedCPF:
    """Create sample enriched CPF"""
    personal_info = create_sample_personal_info()
    employment_history = create_sample_employment_history()
    education_history = create_sample_education_history()
    
    declared_info = CandidateDeclaredInfo(
        personal_info=personal_info,
        current_address=create_sample_address(),
        permanent_address=create_sample_address(),
        employment_history=employment_history,
        education_history=education_history,
        references=[
            {
                "name": "Jane Smith",
                "title": "Engineering Manager",
                "company": "Digital Solutions Corp",
                "email": "<EMAIL>",
                "phone": "******-987-6543"
            },
            {
                "name": "Bob Johnson",
                "title": "Senior Developer",
                "company": "StartupXYZ",
                "email": "<EMAIL>",
                "phone": "******-456-7890"
            }
        ],
        skills=[
            "Python", "JavaScript", "React", "Node.js", "AWS", 
            "Docker", "Kubernetes", "Machine Learning", "SQL"
        ],
        certifications=[
            {
                "name": "AWS Certified Solutions Architect",
                "issuer": "Amazon Web Services",
                "issue_date": "2022-03-15",
                "expiry_date": "2025-03-15"
            }
        ]
    )
    
    government_verification = GovernmentVerification(
        national_id_verified=True,
        passport_verified=True,
        address_verified=True,
        employment_verified=False,
        education_verified=True,
        verification_date=date(2023, 11, 1),
        verification_authority="Department of Homeland Security",
        verification_reference="DHS-2023-11-001234"
    )
    
    return EnrichedCPF(
        candidate_id="CAND_JD_20231201",
        declared_info=declared_info,
        government_verification=government_verification,
        data_sources=[
            "candidate_application",
            "government_database",
            "previous_employer_verification"
        ],
        data_confidence={
            "personal_info": 0.95,
            "employment_history": 0.85,
            "education_history": 0.90
        },
        collection_date=date(2023, 12, 1),
        last_updated=date(2023, 12, 1)
    )


def create_sample_client_input() -> ClientInput:
    """Create sample client input"""
    expected_personal_info = ExpectedPersonalInfo(
        full_name="John Michael Doe",
        alternative_names=["John Doe", "J. Michael Doe"],
        date_of_birth=date(1990, 5, 15),
        national_id="***********",
        email="<EMAIL>",
        phone="******-123-4567",
        current_address=create_sample_address()
    )
    
    expected_employment = [
        ExpectedEmployment(
            employer_name="Tech Innovations Inc.",
            job_title="Senior Software Engineer",
            start_date=date(2021, 3, 1),
            min_salary=90000.0,
            max_salary=100000.0,
            currency="USD",
            expected_location=Address(
                city="San Francisco",
                state="CA",
                country="USA"
            ),
            employment_type="Full-time",
            priority="critical",
            must_verify=True
        ),
        ExpectedEmployment(
            employer_name="Digital Solutions Corp",
            job_title="Software Engineer",
            start_date=date(2019, 6, 15),
            end_date=date(2021, 2, 28),
            min_salary=70000.0,
            max_salary=80000.0,
            currency="USD",
            priority="high",
            must_verify=True
        )
    ]
    
    expected_education = [
        ExpectedEducation(
            institution_name="Massachusetts Institute of Technology",
            degree="Master of Science",
            field_of_study="Computer Science",
            graduation_date=date(2018, 5, 31),
            min_grade="3.5 GPA",
            priority="high",
            must_verify=True
        ),
        ExpectedEducation(
            institution_name="University of California, Berkeley",
            degree="Bachelor of Science",
            field_of_study="Computer Science",
            graduation_date=date(2016, 5, 20),
            priority="medium",
            must_verify=True
        )
    ]
    
    verification_requirements = VerificationRequirements(
        employment_verification_required=True,
        education_verification_required=True,
        identity_verification_required=True,
        address_verification_required=True,
        criminal_background_check=False,
        credit_check=False,
        reference_check=True,
        verification_deadline=date(2023, 12, 15),
        priority_level="high",
        compliance_standards=["SOX", "GDPR"]
    )
    
    return ClientInput(
        client_id="CLIENT_TECHCORP_001",
        candidate_id="CAND_JD_20231201",
        job_position="Senior Software Engineer",
        expected_personal_info=expected_personal_info,
        expected_employment=expected_employment,
        expected_education=expected_education,
        verification_requirements=verification_requirements,
        hiring_context={
            "department": "Engineering",
            "team": "Backend Development",
            "security_clearance_required": False,
            "remote_work_eligible": True
        },
        special_instructions=[
            "Verify current employment status",
            "Confirm education credentials",
            "Check for any employment gaps"
        ],
        verification_budget=500.0
    )


def create_sample_validation_matrix() -> ValidationMatrix:
    """Create sample validation matrix"""
    
    # Employment validation rules
    employer_name_rule = ValidationRule(
        field_name="employer_name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.85,
        priority="critical",
        weight=1.0,
        is_required=True,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    start_date_rule = ValidationRule(
        field_name="start_date",
        field_type=FieldType.DATE,
        match_type=MatchType.DATE_TOLERANCE,
        date_tolerance_days=30,
        priority="high",
        weight=0.8,
        is_required=True,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    salary_rule = ValidationRule(
        field_name="salary",
        field_type=FieldType.NUMBER,
        match_type=MatchType.RANGE,
        numeric_tolerance_percent=15.0,
        priority="medium",
        weight=0.6,
        is_required=False,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    employment_rules = EmploymentValidationRules(
        employer_name_rule=employer_name_rule,
        start_date_rule=start_date_rule,
        salary_rule=salary_rule,
        allow_employment_gaps=True,
        max_gap_days=90,
        require_current_employment=True,
        allow_overlapping_employment=False,
        max_overlap_days=30
    )
    
    # Education validation rules
    institution_name_rule = ValidationRule(
        field_name="institution_name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.8,
        priority="high",
        weight=1.0,
        is_required=True,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    degree_rule = ValidationRule(
        field_name="degree",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.85,
        priority="high",
        weight=0.9,
        is_required=True,
        green_threshold=0.9,
        amber_threshold=0.7
    )
    
    education_rules = EducationValidationRules(
        institution_name_rule=institution_name_rule,
        degree_rule=degree_rule,
        require_degree_verification=True,
        accept_equivalent_degrees=True,
        minimum_grade_required="3.0 GPA"
    )
    
    # Personal info validation rules
    name_rule = ValidationRule(
        field_name="name",
        field_type=FieldType.TEXT,
        match_type=MatchType.FUZZY,
        similarity_threshold=0.9,
        priority="critical",
        weight=1.0,
        is_required=True,
        green_threshold=0.95,
        amber_threshold=0.8
    )
    
    national_id_rule = ValidationRule(
        field_name="national_id",
        field_type=FieldType.TEXT,
        match_type=MatchType.EXACT,
        priority="critical",
        weight=1.0,
        is_required=True,
        green_threshold=1.0,
        amber_threshold=0.0
    )
    
    personal_info_rules = PersonalInfoValidationRules(
        name_rule=name_rule,
        national_id_rule=national_id_rule,
        allow_name_variations=True,
        check_alternative_names=True
    )
    
    # Compliance rules
    compliance_rules = ComplianceRules(
        data_retention_days=2555,  # 7 years
        require_consent=True,
        gdpr_compliant=True,
        sox_compliant=True,
        audit_trail_required=True,
        log_all_access=True,
        encrypt_sensitive_data=True
    )
    
    return ValidationMatrix(
        company_id="CLIENT_TECHCORP_001",
        matrix_name="Tech Corp Standard Verification Matrix",
        version="2.1",
        employment_rules=employment_rules,
        education_rules=education_rules,
        personal_info_rules=personal_info_rules,
        overall_scoring_weights={
            "employment": 0.4,
            "education": 0.3,
            "personal_info": 0.3
        },
        global_green_threshold=0.9,
        global_amber_threshold=0.7,
        human_review_threshold=0.6,
        compliance_rules=compliance_rules,
        industry_type="Technology",
        regulatory_requirements=["SOX", "GDPR", "CCPA"]
    )


# Test scenarios
def create_perfect_match_scenario() -> Dict[str, Any]:
    """Create a scenario where everything matches perfectly"""
    return {
        "enriched_cpf": create_sample_enriched_cpf(),
        "client_input": create_sample_client_input(),
        "validation_matrix": create_sample_validation_matrix(),
        "expected_outcome": {
            "overall_score": 0.95,
            "overall_color_code": "Green",
            "requires_human_review": False
        }
    }


def create_discrepancy_scenario() -> Dict[str, Any]:
    """Create a scenario with some discrepancies"""
    enriched_cpf = create_sample_enriched_cpf()
    client_input = create_sample_client_input()
    validation_matrix = create_sample_validation_matrix()
    
    # Modify data to create discrepancies
    # Change employer name slightly
    enriched_cpf.declared_info.employment_history[0].employer_name = "Tech Innovation Inc."  # Missing 's'
    
    # Change salary to be outside expected range
    enriched_cpf.declared_info.employment_history[0].salary = 110000.0  # Above max expected
    
    # Change education grade
    enriched_cpf.declared_info.education_history[0].grade = "3.4 GPA"  # Below minimum expected
    
    return {
        "enriched_cpf": enriched_cpf,
        "client_input": client_input,
        "validation_matrix": validation_matrix,
        "expected_outcome": {
            "overall_score": 0.75,
            "overall_color_code": "Amber",
            "requires_human_review": True
        }
    }


def create_major_issues_scenario() -> Dict[str, Any]:
    """Create a scenario with major verification issues"""
    enriched_cpf = create_sample_enriched_cpf()
    client_input = create_sample_client_input()
    validation_matrix = create_sample_validation_matrix()
    
    # Create major discrepancies
    # Wrong employer name
    enriched_cpf.declared_info.employment_history[0].employer_name = "Completely Different Corp"
    
    # Wrong education institution
    enriched_cpf.declared_info.education_history[0].institution_name = "Unknown University"
    
    # Wrong national ID
    enriched_cpf.declared_info.personal_info.national_id = "999-99-9999"
    
    return {
        "enriched_cpf": enriched_cpf,
        "client_input": client_input,
        "validation_matrix": validation_matrix,
        "expected_outcome": {
            "overall_score": 0.3,
            "overall_color_code": "Red",
            "requires_human_review": True
        }
    }


# Export all sample data
SAMPLE_SCENARIOS = {
    "perfect_match": create_perfect_match_scenario,
    "minor_discrepancies": create_discrepancy_scenario,
    "major_issues": create_major_issues_scenario
}
