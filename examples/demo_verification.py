#!/usr/bin/env python3
"""
Demo script for the ML Background Verification System

This script demonstrates how to use the verification system with sample data.
Run this after starting the API server to see the system in action.

Usage:
    python examples/demo_verification.py
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.sample_data import (
    create_perfect_match_scenario,
    create_discrepancy_scenario,
    create_major_issues_scenario
)


class VerificationDemo:
    """Demo class for running verification scenarios"""
    
    def __init__(self, api_url="http://localhost:8000"):
        self.api_url = api_url
        
    def run_demo(self):
        """Run all demo scenarios"""
        print("=" * 60)
        print("ML Background Verification System - Demo")
        print("=" * 60)
        print()
        
        # Check if API is available
        if not self.check_api_health():
            print("❌ API is not available. Please start the server first:")
            print("   python -m src.api.main")
            return
        
        print("✅ API is healthy and ready")
        print()
        
        # Run scenarios
        scenarios = [
            ("Perfect Match Scenario", create_perfect_match_scenario()),
            ("Minor Discrepancies Scenario", create_discrepancy_scenario()),
            ("Major Issues Scenario", create_major_issues_scenario())
        ]
        
        for scenario_name, scenario_data in scenarios:
            print(f"🔍 Running: {scenario_name}")
            print("-" * 40)
            
            try:
                result = self.run_verification_scenario(scenario_data)
                self.display_results(result)
            except Exception as e:
                print(f"❌ Scenario failed: {str(e)}")
            
            print()
            time.sleep(2)  # Brief pause between scenarios
        
        print("Demo completed! 🎉")
    
    def check_api_health(self):
        """Check if the API is healthy"""
        try:
            import requests
            response = requests.get(f"{self.api_url}/health", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def run_verification_scenario(self, scenario_data):
        """Run a single verification scenario"""
        import requests
        
        # Extract data from scenario
        enriched_cpf = scenario_data["enriched_cpf"]
        client_input = scenario_data["client_input"]
        validation_matrix = scenario_data["validation_matrix"]
        
        # Prepare request
        verification_request = {
            "enriched_cpf": enriched_cpf.dict() if hasattr(enriched_cpf, 'dict') else enriched_cpf,
            "client_input": client_input.dict() if hasattr(client_input, 'dict') else client_input,
            "validation_matrix": validation_matrix.dict() if hasattr(validation_matrix, 'dict') else validation_matrix
        }
        
        # Submit verification
        print("📤 Submitting verification request...")
        response = requests.post(f"{self.api_url}/verify", json=verification_request)
        response.raise_for_status()
        
        job_data = response.json()
        job_id = job_data["job_id"]
        print(f"   Job ID: {job_id}")
        
        # Poll for completion
        print("⏳ Waiting for verification to complete...")
        max_wait = 120  # 2 minutes
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            status_response = requests.get(f"{self.api_url}/status/{job_id}")
            status_data = status_response.json()
            
            status = status_data["status"]
            progress = status_data["progress_percentage"]
            
            print(f"   Status: {status} ({progress}%)")
            
            if status == "completed":
                break
            elif status == "failed":
                raise Exception("Verification failed")
            
            time.sleep(3)
        else:
            raise Exception("Verification timed out")
        
        # Get results
        print("📥 Retrieving results...")
        results_response = requests.get(f"{self.api_url}/results/{job_id}")
        results_response.raise_for_status()
        
        return results_response.json()
    
    def display_results(self, results):
        """Display verification results in a readable format"""
        verification_results = results["verification_results"]
        summary = results.get("summary", {})
        
        # Overall results
        print(f"📊 Overall Results:")
        print(f"   Score: {verification_results['overall_score']:.2f}")
        print(f"   Color: {self.colorize_result(verification_results['overall_color_code'])}")
        print(f"   Human Review: {'Yes' if verification_results['requires_human_review'] else 'No'}")
        print()
        
        # Field results
        print("📋 Field Results:")
        for field_result in verification_results["field_results"]:
            color_icon = self.get_color_icon(field_result["color_code"])
            print(f"   {color_icon} {field_result['field_name']}: "
                  f"{field_result['color_code']} ({field_result['confidence_score']:.2f})")
            print(f"      {field_result['remark']}")
        print()
        
        # Summary information
        if summary:
            print("📈 Summary:")
            field_summary = summary.get("field_summary", {})
            print(f"   Total Fields: {field_summary.get('total_fields', 0)}")
            print(f"   Green: {field_summary.get('green_count', 0)}, "
                  f"Amber: {field_summary.get('amber_count', 0)}, "
                  f"Red: {field_summary.get('red_count', 0)}")
            print(f"   Risk Level: {summary.get('risk_level', 'Unknown')}")
            
            # Critical issues
            critical_issues = summary.get("critical_issues", [])
            if critical_issues:
                print("   Critical Issues:")
                for issue in critical_issues:
                    print(f"     - {issue['field']}: {issue['issue']}")
            
            # Recommendations
            recommendations = summary.get("recommendations", {})
            if recommendations:
                hiring_decision = recommendations.get("hiring_decision", "Unknown")
                print(f"   Recommendation: {hiring_decision}")
        
        print(f"⏱️  Processing Time: {results.get('processing_time_seconds', 0):.1f} seconds")
    
    def colorize_result(self, color_code):
        """Add color formatting to result text"""
        colors = {
            "Green": "🟢 Green",
            "Amber": "🟡 Amber", 
            "Red": "🔴 Red"
        }
        return colors.get(color_code, color_code)
    
    def get_color_icon(self, color_code):
        """Get icon for color code"""
        icons = {
            "Green": "✅",
            "Amber": "⚠️",
            "Red": "❌"
        }
        return icons.get(color_code, "❓")


async def run_async_demo():
    """Run demo with async capabilities"""
    import aiohttp
    
    demo = VerificationDemo()
    
    print("🚀 Running Async Demo")
    print("=" * 40)
    
    # Create multiple scenarios
    scenarios = [
        create_perfect_match_scenario(),
        create_discrepancy_scenario(),
        create_major_issues_scenario()
    ]
    
    async with aiohttp.ClientSession() as session:
        # Submit all verifications concurrently
        tasks = []
        for i, scenario in enumerate(scenarios):
            task = demo.submit_verification_async(session, scenario, f"Scenario {i+1}")
            tasks.append(task)
        
        # Wait for all to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Display results
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Scenario {i+1} failed: {result}")
            else:
                print(f"Scenario {i+1} completed successfully")
                # Display result summary here


def create_sample_request():
    """Create a simple sample request for testing"""
    return {
        "enriched_cpf": {
            "candidate_id": "DEMO_CANDIDATE",
            "declared_info": {
                "personal_info": {
                    "full_name": "Demo User",
                    "email": "<EMAIL>"
                },
                "employment_history": [
                    {
                        "employer_name": "Demo Company",
                        "job_title": "Demo Position",
                        "start_date": "2020-01-01",
                        "salary": 75000
                    }
                ]
            }
        },
        "client_input": {
            "client_id": "DEMO_CLIENT",
            "candidate_id": "DEMO_CANDIDATE",
            "expected_personal_info": {
                "full_name": "Demo User",
                "email": "<EMAIL>"
            },
            "expected_employment": [
                {
                    "employer_name": "Demo Company",
                    "min_salary": 70000,
                    "max_salary": 80000,
                    "must_verify": True
                }
            ]
        },
        "validation_matrix": {
            "company_id": "DEMO_CLIENT",
            "matrix_name": "Demo Matrix",
            "employment_rules": {
                "employer_name_rule": {
                    "field_name": "employer_name",
                    "field_type": "text",
                    "match_type": "fuzzy",
                    "similarity_threshold": 0.8,
                    "green_threshold": 0.9,
                    "amber_threshold": 0.7
                }
            },
            "personal_info_rules": {
                "name_rule": {
                    "field_name": "name",
                    "field_type": "text",
                    "match_type": "fuzzy",
                    "similarity_threshold": 0.9,
                    "green_threshold": 0.95,
                    "amber_threshold": 0.8
                }
            }
        }
    }


def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ML Background Verification Demo")
    parser.add_argument("--api-url", default="http://localhost:8000", 
                       help="API URL (default: http://localhost:8000)")
    parser.add_argument("--async", action="store_true", 
                       help="Run async demo")
    parser.add_argument("--simple", action="store_true",
                       help="Run simple demo with basic data")
    
    args = parser.parse_args()
    
    if args.simple:
        # Run simple demo with basic request
        demo = VerificationDemo(args.api_url)
        print("🔍 Running Simple Demo")
        print("-" * 30)
        
        try:
            simple_scenario = {"enriched_cpf": create_sample_request()["enriched_cpf"]}
            # Add other required fields...
            result = demo.run_verification_scenario(simple_scenario)
            demo.display_results(result)
        except Exception as e:
            print(f"❌ Demo failed: {str(e)}")
    
    elif args.async:
        # Run async demo
        asyncio.run(run_async_demo())
    
    else:
        # Run standard demo
        demo = VerificationDemo(args.api_url)
        demo.run_demo()


if __name__ == "__main__":
    main()
