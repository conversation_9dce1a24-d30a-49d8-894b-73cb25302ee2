#!/usr/bin/env python3
"""
Test script for AWS deployment of ML Background Verification System

This script tests the deployed AWS infrastructure by:
1. Creating a test verification job in DynamoDB
2. Sending a message to SQS to trigger processing
3. Monitoring the job status
4. Retrieving and displaying results

Usage:
    python examples/test_aws_deployment.py --environment dev
"""

import argparse
import asyncio
import boto3
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

# AWS clients
dynamodb = None
sqs = None
lambda_client = None

# Configuration
REGION = 'us-east-1'
TABLE_NAME = 'Resume-PII-Processor-table'
QUEUE_NAME_TEMPLATE = 'verification-jobs-queue-{}'
LAMBDA_FUNCTION_TEMPLATE = 'verification-processor-{}'


def initialize_aws_clients(region: str = REGION):
    """Initialize AWS clients"""
    global dynamodb, sqs, lambda_client
    
    dynamodb = boto3.resource('dynamodb', region_name=region)
    sqs = boto3.client('sqs', region_name=region)
    lambda_client = boto3.client('lambda', region_name=region)
    
    print(f"✅ Initialized AWS clients for region: {region}")


def create_test_data() -> Dict[str, Any]:
    """Create test verification data"""
    return {
        "cpf": {
            "candidate_id": "TEST_CANDIDATE_001",
            "declared_info": {
                "personal_info": {
                    "full_name": "Alice Johnson",
                    "email": "<EMAIL>",
                    "phone": "******-123-4567",
                    "national_id": "123-45-6789"
                },
                "employment_history": [
                    {
                        "employer_name": "TechCorp Solutions",
                        "job_title": "Senior Software Engineer",
                        "start_date": "2021-03-01",
                        "end_date": None,
                        "salary": 95000,
                        "currency": "USD"
                    },
                    {
                        "employer_name": "Innovation Labs",
                        "job_title": "Software Engineer",
                        "start_date": "2019-06-15",
                        "end_date": "2021-02-28",
                        "salary": 75000,
                        "currency": "USD"
                    }
                ],
                "education_history": [
                    {
                        "institution_name": "State University",
                        "degree": "Bachelor of Science",
                        "field_of_study": "Computer Science",
                        "start_date": "2015-09-01",
                        "end_date": "2019-05-31",
                        "grade": "3.7 GPA"
                    }
                ]
            },
            "government_verification": {
                "national_id_verified": True,
                "employment_verified": False,
                "education_verified": True
            }
        },
        "client_input": {
            "client_id": "TEST_CLIENT_001",
            "candidate_id": "TEST_CANDIDATE_001",
            "expected_personal_info": {
                "full_name": "Alice Johnson",
                "email": "<EMAIL>",
                "national_id": "123-45-6789"
            },
            "expected_employment": [
                {
                    "employer_name": "TechCorp Solutions",
                    "job_title": "Senior Software Engineer",
                    "start_date": "2021-03-01",
                    "min_salary": 90000,
                    "max_salary": 100000,
                    "must_verify": True
                }
            ],
            "expected_education": [
                {
                    "institution_name": "State University",
                    "degree": "Bachelor of Science",
                    "field_of_study": "Computer Science",
                    "must_verify": True
                }
            ],
            "verification_requirements": {
                "employment_verification_required": True,
                "education_verification_required": True,
                "identity_verification_required": True
            }
        },
        "validation_matrix": {
            "company_id": "TEST_CLIENT_001",
            "matrix_name": "Test Verification Matrix",
            "employment_rules": {
                "employer_name_rule": {
                    "field_name": "employer_name",
                    "field_type": "text",
                    "match_type": "fuzzy",
                    "similarity_threshold": 0.8,
                    "green_threshold": 0.9,
                    "amber_threshold": 0.7
                },
                "start_date_rule": {
                    "field_name": "start_date",
                    "field_type": "date",
                    "match_type": "date_tolerance",
                    "date_tolerance_days": 30,
                    "green_threshold": 0.9,
                    "amber_threshold": 0.7
                }
            },
            "education_rules": {
                "institution_name_rule": {
                    "field_name": "institution_name",
                    "field_type": "text",
                    "match_type": "fuzzy",
                    "similarity_threshold": 0.8,
                    "green_threshold": 0.9,
                    "amber_threshold": 0.7
                }
            },
            "personal_info_rules": {
                "name_rule": {
                    "field_name": "name",
                    "field_type": "text",
                    "match_type": "fuzzy",
                    "similarity_threshold": 0.9,
                    "green_threshold": 0.95,
                    "amber_threshold": 0.8
                }
            }
        }
    }


def create_verification_job(environment: str) -> str:
    """Create a verification job in DynamoDB"""
    job_id = str(uuid.uuid4())
    test_data = create_test_data()
    
    table = dynamodb.Table(TABLE_NAME)
    
    try:
        table.put_item(
            Item={
                'job_id': job_id,
                'cpf': json.dumps(test_data['cpf']),
                'client_input': json.dumps(test_data['client_input']),
                'validation_matrix': json.dumps(test_data['validation_matrix']),
                'job_status': 'pending',
                'created_at': datetime.utcnow().isoformat(),
                'candidate_id': test_data['cpf']['candidate_id'],
                'client_id': test_data['client_input']['client_id']
            }
        )
        
        print(f"✅ Created verification job: {job_id}")
        return job_id
        
    except Exception as e:
        print(f"❌ Failed to create verification job: {str(e)}")
        raise


def send_sqs_message(job_id: str, environment: str) -> bool:
    """Send SQS message to trigger processing"""
    queue_name = QUEUE_NAME_TEMPLATE.format(environment)
    
    try:
        # Get queue URL
        response = sqs.get_queue_url(QueueName=queue_name)
        queue_url = response['QueueUrl']
        
        # Send message
        sqs.send_message(
            QueueUrl=queue_url,
            MessageBody=json.dumps({
                'job_id': job_id,
                'timestamp': datetime.utcnow().isoformat()
            }),
            MessageAttributes={
                'JobId': {
                    'StringValue': job_id,
                    'DataType': 'String'
                }
            }
        )
        
        print(f"✅ Sent SQS message for job: {job_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to send SQS message: {str(e)}")
        return False


def get_job_status(job_id: str) -> Optional[Dict[str, Any]]:
    """Get job status from DynamoDB"""
    table = dynamodb.Table(TABLE_NAME)
    
    try:
        response = table.get_item(Key={'job_id': job_id})
        return response.get('Item')
    except Exception as e:
        print(f"❌ Failed to get job status: {str(e)}")
        return None


def monitor_job(job_id: str, timeout_seconds: int = 300) -> Optional[Dict[str, Any]]:
    """Monitor job until completion or timeout"""
    print(f"🔍 Monitoring job: {job_id}")
    
    start_time = time.time()
    
    while time.time() - start_time < timeout_seconds:
        job_data = get_job_status(job_id)
        
        if not job_data:
            print("❌ Job not found")
            return None
        
        status = job_data.get('job_status', 'unknown')
        print(f"   Status: {status}")
        
        if status == 'completed':
            print("✅ Job completed successfully!")
            return job_data
        elif status == 'failed':
            error_msg = job_data.get('error_message', 'Unknown error')
            print(f"❌ Job failed: {error_msg}")
            return job_data
        
        time.sleep(10)  # Wait 10 seconds before checking again
    
    print(f"⏰ Job monitoring timed out after {timeout_seconds} seconds")
    return get_job_status(job_id)


def display_results(job_data: Dict[str, Any]):
    """Display verification results"""
    if not job_data:
        print("❌ No job data to display")
        return
    
    print("\n" + "="*60)
    print("VERIFICATION RESULTS")
    print("="*60)
    
    print(f"Job ID: {job_data['job_id']}")
    print(f"Status: {job_data.get('job_status', 'unknown')}")
    print(f"Created: {job_data.get('created_at', 'unknown')}")
    
    if job_data.get('completed_at'):
        print(f"Completed: {job_data['completed_at']}")
    
    if job_data.get('verification_result'):
        try:
            result = json.loads(job_data['verification_result'])
            
            print(f"\nOverall Score: {result.get('overall_score', 'N/A')}")
            print(f"Overall Color: {result.get('overall_color_code', 'N/A')}")
            print(f"Human Review Required: {result.get('requires_human_review', 'N/A')}")
            print(f"Processing Time: {result.get('processing_time_seconds', 'N/A')} seconds")
            
            if result.get('field_results'):
                print(f"\nField Results ({len(result['field_results'])} fields):")
                for field_result in result['field_results']:
                    color_icon = {"Green": "🟢", "Amber": "🟡", "Red": "🔴"}.get(
                        field_result.get('color_code', ''), "❓"
                    )
                    print(f"  {color_icon} {field_result.get('field_name', 'unknown')}: "
                          f"{field_result.get('color_code', 'unknown')} "
                          f"({field_result.get('confidence_score', 0):.2f})")
            
            if result.get('summary'):
                print(f"\nSummary: {result['summary']}")
                
        except json.JSONDecodeError:
            print("❌ Failed to parse verification result")
    
    print("="*60)


def test_lambda_directly(job_id: str, environment: str) -> bool:
    """Test Lambda function directly"""
    function_name = LAMBDA_FUNCTION_TEMPLATE.format(environment)
    
    try:
        response = lambda_client.invoke(
            FunctionName=function_name,
            Payload=json.dumps({'job_id': job_id})
        )
        
        payload = json.loads(response['Payload'].read())
        print(f"✅ Lambda invocation successful")
        print(f"   Response: {payload}")
        return True
        
    except Exception as e:
        print(f"❌ Lambda invocation failed: {str(e)}")
        return False


def test_health_check(environment: str) -> bool:
    """Test health check function"""
    function_name = f"verification-health-check-{environment}"
    
    try:
        response = lambda_client.invoke(
            FunctionName=function_name,
            Payload=json.dumps({})
        )
        
        payload = json.loads(response['Payload'].read())
        print(f"✅ Health check successful")
        
        if 'body' in payload:
            body = json.loads(payload['body'])
            print(f"   Status: {body.get('status', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Test AWS deployment')
    parser.add_argument('--environment', '-e', default='dev', 
                       help='Environment to test (dev, staging, prod)')
    parser.add_argument('--region', '-r', default=REGION,
                       help='AWS region')
    parser.add_argument('--direct-lambda', action='store_true',
                       help='Test Lambda function directly instead of SQS')
    parser.add_argument('--health-only', action='store_true',
                       help='Only run health check')
    
    args = parser.parse_args()
    
    print(f"🚀 Testing AWS deployment")
    print(f"Environment: {args.environment}")
    print(f"Region: {args.region}")
    print("-" * 40)
    
    # Initialize AWS clients
    initialize_aws_clients(args.region)
    
    # Health check only
    if args.health_only:
        success = test_health_check(args.environment)
        return 0 if success else 1
    
    # Create verification job
    job_id = create_verification_job(args.environment)
    
    if args.direct_lambda:
        # Test Lambda directly
        print("\n🔧 Testing Lambda function directly...")
        test_lambda_directly(job_id, args.environment)
        
        # Wait a bit and check results
        time.sleep(30)
        job_data = get_job_status(job_id)
        display_results(job_data)
    else:
        # Test via SQS
        print("\n📤 Testing via SQS...")
        if send_sqs_message(job_id, args.environment):
            # Monitor job completion
            job_data = monitor_job(job_id)
            display_results(job_data)
        else:
            print("❌ Failed to send SQS message")
            return 1
    
    print("\n🎉 Test completed!")
    return 0


if __name__ == "__main__":
    exit(main())
