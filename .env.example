# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# OpenAI Configuration (for prompt engineering)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database Configuration (Optional)
DATABASE_URL=sqlite:///./verification_results.db

# Verification Thresholds
CONFIDENCE_THRESHOLD_GREEN=0.9
CONFIDENCE_THRESHOLD_AMBER=0.7
HUMAN_REVIEW_THRESHOLD=0.6

# Security
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
