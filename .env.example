# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here

# AWS Bedrock Configuration (for Mistral 7B Instruct)
BEDROCK_MODEL_ID=mistral.mistral-7b-instruct-v0:2
BEDROCK_MAX_TOKENS=2000
BEDROCK_TEMPERATURE=0.1

# DynamoDB Configuration
# DYNAMODB_TABLE_NAME=Resume-PII-Processor-table
DYNAMODB_TABLE_NAME=Resume-table-test

# SQS Configuration
SQS_QUEUE_NAME=verification-jobs-queue

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database Configuration (Optional)
DATABASE_URL=sqlite:///./verification_results.db

# Verification Thresholds
CONFIDENCE_THRESHOLD_GREEN=0.9
CONFIDENCE_THRESHOLD_AMBER=0.7
HUMAN_REVIEW_THRESHOLD=0.6

# Security
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
