{"job_id": "job_12345", "enriched_cpf": {"candidate_id": "CAND_12345", "declared_info": {"personal_info": {"full_name": "<PERSON>", "email": "<EMAIL>", "phone": "+*********0", "national_id": "*********"}, "employment_history": [{"employer_name": "Tech Corp", "job_title": "Software Engineer", "start_date": "2020-01-01", "end_date": "2023-12-31", "salary": 75000, "currency": "USD"}], "education_history": [{"institution_name": "University of Technology", "degree": "Bachelor of Science", "field_of_study": "Computer Science", "start_date": "2016-09-01", "end_date": "2020-05-31"}]}, "government_verification": {"national_id_verified": true, "employment_verified": false, "education_verified": true}}, "client_input": {"client_id": "CLIENT_001", "candidate_id": "CAND_12345", "expected_personal_info": {"full_name": "<PERSON>", "email": "<EMAIL>", "national_id": "*********"}, "expected_employment": [{"employer_name": "Tech Corp", "min_salary": 70000, "max_salary": 80000, "must_verify": true}], "expected_education": [{"institution_name": "University of Technology", "degree": "Bachelor of Science", "must_verify": true}], "verification_requirements": {"employment_verification_required": true, "education_verification_required": true, "identity_verification_required": true}}, "validation_matrix": {"company_id": "CLIENT_001", "matrix_name": "Standard Verification", "employment_rules": {"employer_name_rule": {"field_name": "employer_name", "field_type": "text", "match_type": "fuzzy", "similarity_threshold": 0.8, "green_threshold": 0.9, "amber_threshold": 0.7}, "start_date_rule": {"field_name": "start_date", "field_type": "date", "match_type": "date_tolerance", "date_tolerance_days": 30, "green_threshold": 0.9, "amber_threshold": 0.7}}, "education_rules": {"institution_name_rule": {"field_name": "institution_name", "field_type": "text", "match_type": "fuzzy", "similarity_threshold": 0.8, "green_threshold": 0.9, "amber_threshold": 0.7}}, "personal_info_rules": {"name_rule": {"field_name": "name", "field_type": "text", "match_type": "fuzzy", "similarity_threshold": 0.9, "green_threshold": 0.95, "amber_threshold": 0.8}}}}