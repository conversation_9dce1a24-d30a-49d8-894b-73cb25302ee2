# AWS Deployment Guide

## Overview

This guide covers deploying the ML Background Verification System to AWS using:
- **AWS Lambda** for serverless compute
- **AWS Bedrock** with Mistral 7B Instruct model for AI analysis
- **Amazon DynamoDB** for data storage
- **Amazon SQS** for job queuing
- **AWS CloudFormation** and **Serverless Framework** for infrastructure as code

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   SQS Queue     │───▶│  Lambda Function│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DynamoDB      │◀───│  Verification   │───▶│  AWS Bedrock    │
│   Table         │    │  Engine         │    │  (Mistral 7B)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Prerequisites

### 1. AWS Account Setup
- AWS Account with appropriate permissions
- AWS CLI installed and configured
- Access to AWS Bedrock with Mistral 7B model enabled

### 2. Development Tools
- Node.js (v16 or later) for Serverless Framework
- Python 3.9 or later
- Serverless Framework CLI

### 3. AWS Permissions
Your AWS user/role needs the following permissions:
- CloudFormation full access
- Lambda full access
- DynamoDB full access
- SQS full access
- <PERSON><PERSON> invoke model permissions
- IAM role creation permissions
- <PERSON>Watch logs access

## Installation

### 1. Install Dependencies

```bash
# Install Serverless Framework globally
npm install -g serverless

# Install Python dependencies
pip install -r requirements.txt

# Install Serverless plugins (in aws/ directory)
cd aws
npm install
cd ..
```

### 2. Configure AWS Credentials

```bash
aws configure
# Enter your AWS Access Key ID, Secret Access Key, and region
```

### 3. Enable AWS Bedrock Access

Ensure you have access to the Mistral 7B Instruct model in AWS Bedrock:

1. Go to AWS Bedrock console
2. Navigate to "Model access" 
3. Request access to `mistral.mistral-7b-instruct-v0:2`
4. Wait for approval (usually immediate for most models)

## Deployment

### Quick Deployment

Use the provided deployment script:

```bash
# Deploy to development environment
./scripts/deploy.sh dev

# Deploy to production environment  
./scripts/deploy.sh prod us-west-2
```

### Manual Deployment

#### Step 1: Deploy DynamoDB Table

```bash
aws cloudformation deploy \
  --template-file aws/dynamodb-table.yml \
  --stack-name ml-verification-infrastructure-dev \
  --parameter-overrides Environment=dev \
  --capabilities CAPABILITY_NAMED_IAM \
  --region us-east-1
```

#### Step 2: Deploy Lambda Functions

```bash
cd aws
serverless deploy --stage dev --region us-east-1
cd ..
```

## Configuration

### Environment Variables

The system uses the following environment variables:

```bash
# AWS Configuration
AWS_REGION=us-east-1

# Bedrock Configuration
BEDROCK_MODEL_ID=mistral.mistral-7b-instruct-v0:2
BEDROCK_MAX_TOKENS=2000
BEDROCK_TEMPERATURE=0.1

# DynamoDB Configuration
DYNAMODB_TABLE_NAME=Resume-PII-Processor-table

# SQS Configuration
SQS_QUEUE_NAME=verification-jobs-queue
```

### DynamoDB Table Schema

The DynamoDB table has the following structure:

**Primary Key:**
- `job_id` (String) - Partition key

**Attributes:**
- `cpf` (String) - JSON string of enriched CPF data
- `client_input` (String) - JSON string of client input data
- `validation_matrix` (String) - JSON string of validation matrix
- `job_status` (String) - Current job status
- `verification_result` (String) - JSON string of verification results
- `created_at` (String) - ISO timestamp
- `updated_at` (String) - ISO timestamp
- `completed_at` (String) - ISO timestamp
- `error_message` (String) - Error message if failed

**Global Secondary Indexes:**
- `candidate-id-index` - Query by candidate_id
- `client-id-index` - Query by client_id  
- `job-status-index` - Query by job_status

## Usage

### 1. Submit Verification Job

To submit a verification job, add a record to the DynamoDB table and send a message to SQS:

```python
import boto3
import json
import uuid

# Initialize clients
dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
sqs = boto3.client('sqs', region_name='us-east-1')

table = dynamodb.Table('Resume-PII-Processor-table')
queue_url = 'https://sqs.us-east-1.amazonaws.com/YOUR-ACCOUNT/verification-jobs-queue-dev'

# Generate job ID
job_id = str(uuid.uuid4())

# Prepare data
cpf_data = {
    "candidate_id": "CAND_12345",
    "declared_info": {
        "personal_info": {
            "full_name": "John Doe",
            "email": "<EMAIL>"
        },
        "employment_history": [
            {
                "employer_name": "Tech Corp",
                "job_title": "Software Engineer",
                "start_date": "2020-01-01",
                "salary": 75000
            }
        ]
    }
}

client_input_data = {
    "client_id": "CLIENT_001",
    "candidate_id": "CAND_12345",
    "expected_personal_info": {
        "full_name": "John Doe",
        "email": "<EMAIL>"
    },
    "expected_employment": [
        {
            "employer_name": "Tech Corp",
            "min_salary": 70000,
            "max_salary": 80000,
            "must_verify": True
        }
    ]
}

validation_matrix_data = {
    "company_id": "CLIENT_001",
    "matrix_name": "Standard Verification",
    "employment_rules": {
        "employer_name_rule": {
            "field_name": "employer_name",
            "field_type": "text",
            "match_type": "fuzzy",
            "similarity_threshold": 0.8
        }
    }
}

# Store in DynamoDB
table.put_item(
    Item={
        'job_id': job_id,
        'cpf': json.dumps(cpf_data),
        'client_input': json.dumps(client_input_data),
        'validation_matrix': json.dumps(validation_matrix_data),
        'job_status': 'pending',
        'created_at': datetime.utcnow().isoformat()
    }
)

# Send SQS message
sqs.send_message(
    QueueUrl=queue_url,
    MessageBody=json.dumps({'job_id': job_id})
)

print(f"Submitted job: {job_id}")
```

### 2. Check Job Status

```python
# Get job status
response = table.get_item(Key={'job_id': job_id})
item = response.get('Item', {})

print(f"Status: {item.get('job_status')}")
if item.get('verification_result'):
    result = json.loads(item['verification_result'])
    print(f"Overall Score: {result['overall_score']}")
    print(f"Color Code: {result['overall_color_code']}")
```

### 3. Direct Lambda Invocation

You can also invoke the Lambda function directly:

```bash
aws lambda invoke \
  --function-name verification-processor-dev \
  --payload '{"job_id": "your-job-id-here"}' \
  response.json

cat response.json
```

## Monitoring

### CloudWatch Dashboard

The deployment creates a CloudWatch dashboard named `ML-Verification-{environment}` with:
- Lambda function metrics (duration, errors, invocations)
- DynamoDB capacity metrics
- SQS queue metrics

### CloudWatch Alarms

The following alarms are automatically created:
- Lambda function errors
- Lambda function duration
- DynamoDB throttling
- SQS queue depth

### Logs

Lambda function logs are available in CloudWatch Logs:
- `/aws/lambda/verification-processor-{environment}`
- `/aws/lambda/verification-health-check-{environment}`
- `/aws/lambda/verification-batch-processor-{environment}`

## Troubleshooting

### Common Issues

1. **Bedrock Access Denied**
   - Ensure you have requested access to Mistral 7B model
   - Check IAM permissions for bedrock:InvokeModel

2. **DynamoDB Throttling**
   - The table uses on-demand billing to avoid throttling
   - Check CloudWatch metrics for throttling events

3. **Lambda Timeout**
   - Default timeout is 15 minutes
   - Check CloudWatch logs for timeout errors
   - Consider increasing memory allocation

4. **SQS Message Processing Failures**
   - Check dead letter queue for failed messages
   - Review Lambda function logs for errors

### Debugging

Enable debug logging by setting environment variable:
```bash
LOG_LEVEL=DEBUG
```

View Lambda logs:
```bash
aws logs tail /aws/lambda/verification-processor-dev --follow
```

## Cost Optimization

### Lambda
- Use provisioned concurrency for consistent performance
- Monitor memory usage and adjust allocation
- Consider using ARM-based Graviton2 processors

### DynamoDB
- Table uses on-demand billing by default
- Consider provisioned capacity for predictable workloads
- Enable point-in-time recovery only if needed

### Bedrock
- Mistral 7B pricing is per token
- Optimize prompts to reduce token usage
- Consider caching results for similar requests

## Security

### Data Encryption
- DynamoDB table encrypted at rest with AWS managed keys
- Lambda environment variables encrypted
- SQS messages encrypted in transit

### Access Control
- IAM roles follow principle of least privilege
- Lambda functions have minimal required permissions
- VPC configuration available for additional isolation

### Compliance
- Enable CloudTrail for audit logging
- Configure data retention policies
- Implement data classification tags

## Scaling

### Automatic Scaling
- Lambda scales automatically up to account limits
- DynamoDB on-demand scales automatically
- SQS handles high message volumes

### Performance Tuning
- Adjust Lambda memory allocation based on usage
- Use SQS batch processing for higher throughput
- Consider Lambda provisioned concurrency for consistent latency

## Cleanup

To remove all deployed resources:

```bash
# Remove Serverless stack
cd aws
serverless remove --stage dev
cd ..

# Remove DynamoDB stack
aws cloudformation delete-stack \
  --stack-name ml-verification-infrastructure-dev \
  --region us-east-1
```

## Support

For issues and questions:
1. Check CloudWatch logs for error details
2. Review AWS service quotas and limits
3. Consult AWS documentation for service-specific issues
4. Use AWS Support for account-level issues
