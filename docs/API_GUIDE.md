# API Guide

## Overview

The ML Background Verification System provides a RESTful API for automating candidate background verification using AI and structured prompt engineering.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API does not require authentication. In production, implement proper authentication mechanisms.

## Endpoints

### Health Check

Check the system health and status.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "version": "1.0.0",
  "services": {
    "verification_engine": "operational",
    "scoring_system": "operational",
    "api": "operational"
  }
}
```

### Submit Verification Request

Submit a new verification request for processing.

**Endpoint:** `POST /verify`

**Request Body:**
```json
{
  "enriched_cpf": {
    "candidate_id": "CAND_12345",
    "declared_info": {
      "personal_info": {
        "full_name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+**********",
        "national_id": "123456789"
      },
      "employment_history": [
        {
          "employer_name": "Tech Corp",
          "job_title": "Software Engineer",
          "start_date": "2020-01-01",
          "end_date": "2023-12-31",
          "salary": 75000,
          "currency": "USD"
        }
      ],
      "education_history": [
        {
          "institution_name": "University of Technology",
          "degree": "Bachelor of Science",
          "field_of_study": "Computer Science",
          "start_date": "2016-09-01",
          "end_date": "2020-05-31"
        }
      ]
    },
    "government_verification": {
      "national_id_verified": true,
      "employment_verified": false,
      "education_verified": true
    }
  },
  "client_input": {
    "client_id": "CLIENT_001",
    "candidate_id": "CAND_12345",
    "expected_personal_info": {
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "national_id": "123456789"
    },
    "expected_employment": [
      {
        "employer_name": "Tech Corp",
        "min_salary": 70000,
        "max_salary": 80000,
        "must_verify": true
      }
    ],
    "expected_education": [
      {
        "institution_name": "University of Technology",
        "degree": "Bachelor of Science",
        "must_verify": true
      }
    ],
    "verification_requirements": {
      "employment_verification_required": true,
      "education_verification_required": true,
      "identity_verification_required": true
    }
  },
  "validation_matrix": {
    "company_id": "CLIENT_001",
    "matrix_name": "Standard Verification",
    "employment_rules": {
      "employer_name_rule": {
        "field_name": "employer_name",
        "field_type": "text",
        "match_type": "fuzzy",
        "similarity_threshold": 0.8,
        "green_threshold": 0.9,
        "amber_threshold": 0.7
      },
      "start_date_rule": {
        "field_name": "start_date",
        "field_type": "date",
        "match_type": "date_tolerance",
        "date_tolerance_days": 30,
        "green_threshold": 0.9,
        "amber_threshold": 0.7
      }
    },
    "education_rules": {
      "institution_name_rule": {
        "field_name": "institution_name",
        "field_type": "text",
        "match_type": "fuzzy",
        "similarity_threshold": 0.8,
        "green_threshold": 0.9,
        "amber_threshold": 0.7
      }
    },
    "personal_info_rules": {
      "name_rule": {
        "field_name": "name",
        "field_type": "text",
        "match_type": "fuzzy",
        "similarity_threshold": 0.9,
        "green_threshold": 0.95,
        "amber_threshold": 0.8
      }
    }
  }
}
```

**Response:**
```json
{
  "job_id": "job_abc123",
  "status": "pending",
  "message": "Verification request submitted successfully",
  "estimated_completion_time": 60,
  "created_at": "2023-12-01T10:00:00Z"
}
```

### Check Verification Status

Check the status of a verification job.

**Endpoint:** `GET /status/{job_id}`

**Response:**
```json
{
  "job_id": "job_abc123",
  "status": "in_progress",
  "created_at": "2023-12-01T10:00:00Z",
  "completed_at": null,
  "progress_percentage": 75,
  "message": "Verification is currently in progress"
}
```

**Status Values:**
- `pending`: Job is queued for processing
- `in_progress`: Job is currently being processed
- `completed`: Job has completed successfully
- `failed`: Job has failed

### Get Verification Results

Retrieve the results of a completed verification job.

**Endpoint:** `GET /results/{job_id}`

**Response:**
```json
{
  "job_id": "job_abc123",
  "status": "completed",
  "candidate_id": "CAND_12345",
  "verification_results": {
    "overall_score": 0.85,
    "overall_color_code": "Amber",
    "requires_human_review": true,
    "summary": "Verification completed for 5 fields. Results: 3 Green, 1 Amber, 1 Red. Overall confidence score: 0.85. Human review recommended due to salary discrepancy.",
    "field_results": [
      {
        "field_name": "employer_name",
        "color_code": "Green",
        "confidence_score": 0.95,
        "remark": "Employer name matches exactly",
        "expected_value": "Tech Corp",
        "actual_value": "Tech Corp",
        "requires_human_review": false
      },
      {
        "field_name": "salary",
        "color_code": "Red",
        "confidence_score": 0.3,
        "remark": "Salary significantly outside range (25.0% variance)",
        "expected_value": "70000-80000",
        "actual_value": 75000,
        "variance": "25.0%",
        "requires_human_review": true
      }
    ]
  },
  "summary": {
    "overall_score": 0.85,
    "overall_color_code": "Amber",
    "risk_level": "Medium",
    "field_summary": {
      "total_fields": 5,
      "green_count": 3,
      "amber_count": 1,
      "red_count": 1
    },
    "critical_issues": [
      {
        "field": "salary",
        "issue": "Salary significantly outside range",
        "expected": "70000-80000",
        "actual": 75000
      }
    ],
    "requires_human_review": true,
    "recommendations": {
      "hiring_decision": "Conditional",
      "additional_verification": ["Employment reference check"],
      "immediate_actions": ["Investigate salary discrepancy"]
    }
  },
  "processing_time_seconds": 45.2,
  "completed_at": "2023-12-01T10:01:00Z"
}
```

### Cancel Verification Job

Cancel a pending or in-progress verification job.

**Endpoint:** `DELETE /jobs/{job_id}`

**Response:**
```json
{
  "message": "Verification job job_abc123 cancelled successfully"
}
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "timestamp": "2023-12-01T10:00:00Z",
  "request_id": "req_xyz789"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `202 Accepted`: Request accepted but not yet complete
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Limit**: 100 requests per minute per IP address
- **Headers**: Rate limit information is included in response headers:
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when rate limit resets

## Request/Response Headers

### Request Headers

- `Content-Type: application/json` (required for POST requests)
- `Accept: application/json` (optional)

### Response Headers

- `X-Request-ID`: Unique identifier for the request
- `X-Process-Time`: Processing time in seconds
- `X-RateLimit-*`: Rate limiting information

## Color Code System

The verification system uses a three-color classification:

- **Green**: Perfect match or within acceptable tolerance (score ≥ 0.9)
- **Amber**: Minor discrepancies that may need attention (score ≥ 0.7)
- **Red**: Significant discrepancies requiring investigation (score < 0.7)

## Confidence Scores

Confidence scores range from 0.0 to 1.0:

- **1.0**: Perfect match, no concerns
- **0.8-0.9**: Strong match, minor discrepancies
- **0.6-0.7**: Moderate match, some concerns
- **0.4-0.5**: Weak match, significant concerns
- **0.0-0.3**: Poor match, major red flags

## Human Review Flags

The system automatically flags cases for human review when:

- Overall confidence score < 0.6
- Any critical field has a Red classification
- Multiple fields have Amber or Red classifications
- Specific validation rules require human review

## Example Workflows

### Basic Verification Workflow

1. Submit verification request: `POST /verify`
2. Poll for status: `GET /status/{job_id}`
3. Retrieve results when complete: `GET /results/{job_id}`

### Batch Processing Workflow

1. Submit multiple verification requests
2. Monitor all job statuses
3. Retrieve results as they complete
4. Aggregate results for reporting

### Error Handling Workflow

1. Submit request with proper error handling
2. Check for validation errors (422)
3. Retry on temporary failures (5xx)
4. Handle rate limiting (429) with backoff
