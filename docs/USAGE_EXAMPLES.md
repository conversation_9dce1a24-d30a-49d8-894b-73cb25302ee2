# Usage Examples

## Overview

This document provides practical examples of using the ML Background Verification System for different scenarios and use cases.

## Basic Usage Examples

### Example 1: Simple Employment Verification

This example shows how to verify a candidate's current employment.

```python
import requests
import json
from datetime import date

# API endpoint
api_url = "http://localhost:8000"

# Prepare verification request
verification_request = {
    "enriched_cpf": {
        "candidate_id": "CAND_001",
        "declared_info": {
            "personal_info": {
                "full_name": "<PERSON>",
                "email": "<EMAIL>",
                "national_id": "123-45-6789"
            },
            "employment_history": [
                {
                    "employer_name": "TechCorp Inc.",
                    "job_title": "Senior Developer",
                    "start_date": "2021-01-15",
                    "end_date": None,  # Current job
                    "salary": 95000,
                    "currency": "USD"
                }
            ]
        },
        "government_verification": {
            "national_id_verified": True,
            "employment_verified": False
        }
    },
    "client_input": {
        "client_id": "HIRING_COMPANY_001",
        "candidate_id": "CAND_001",
        "expected_personal_info": {
            "full_name": "<PERSON>",
            "email": "<EMAIL>"
        },
        "expected_employment": [
            {
                "employer_name": "TechCorp Inc.",
                "job_title": "Senior Developer",
                "start_date": "2021-01-15",
                "min_salary": 90000,
                "max_salary": 100000,
                "must_verify": True
            }
        ],
        "verification_requirements": {
            "employment_verification_required": True,
            "identity_verification_required": True
        }
    },
    "validation_matrix": {
        "company_id": "HIRING_COMPANY_001",
        "matrix_name": "Standard Tech Verification",
        "employment_rules": {
            "employer_name_rule": {
                "field_name": "employer_name",
                "field_type": "text",
                "match_type": "fuzzy",
                "similarity_threshold": 0.85,
                "green_threshold": 0.9,
                "amber_threshold": 0.7
            }
        },
        "personal_info_rules": {
            "name_rule": {
                "field_name": "name",
                "field_type": "text",
                "match_type": "fuzzy",
                "similarity_threshold": 0.9,
                "green_threshold": 0.95,
                "amber_threshold": 0.8
            }
        }
    }
}

# Submit verification request
response = requests.post(f"{api_url}/verify", json=verification_request)
job_data = response.json()
job_id = job_data["job_id"]

print(f"Verification job submitted: {job_id}")

# Poll for completion
import time
while True:
    status_response = requests.get(f"{api_url}/status/{job_id}")
    status_data = status_response.json()
    
    print(f"Status: {status_data['status']} ({status_data['progress_percentage']}%)")
    
    if status_data["status"] == "completed":
        break
    elif status_data["status"] == "failed":
        print("Verification failed!")
        exit(1)
    
    time.sleep(5)

# Get results
results_response = requests.get(f"{api_url}/results/{job_id}")
results = results_response.json()

print(f"Overall Score: {results['verification_results']['overall_score']}")
print(f"Color Code: {results['verification_results']['overall_color_code']}")
print(f"Human Review Required: {results['verification_results']['requires_human_review']}")

for field_result in results['verification_results']['field_results']:
    print(f"- {field_result['field_name']}: {field_result['color_code']} ({field_result['confidence_score']:.2f})")
```

### Example 2: Education Verification

This example focuses on verifying educational credentials.

```python
education_verification_request = {
    "enriched_cpf": {
        "candidate_id": "CAND_002",
        "declared_info": {
            "personal_info": {
                "full_name": "Bob Smith",
                "email": "<EMAIL>"
            },
            "education_history": [
                {
                    "institution_name": "Stanford University",
                    "degree": "Master of Science",
                    "field_of_study": "Computer Science",
                    "start_date": "2018-09-01",
                    "end_date": "2020-06-15",
                    "grade": "3.8 GPA"
                },
                {
                    "institution_name": "UC Berkeley",
                    "degree": "Bachelor of Science",
                    "field_of_study": "Computer Science",
                    "start_date": "2014-08-15",
                    "end_date": "2018-05-20",
                    "grade": "3.6 GPA"
                }
            ]
        },
        "government_verification": {
            "education_verified": True
        }
    },
    "client_input": {
        "client_id": "UNIVERSITY_HIRING_001",
        "candidate_id": "CAND_002",
        "expected_personal_info": {
            "full_name": "Bob Smith"
        },
        "expected_education": [
            {
                "institution_name": "Stanford University",
                "degree": "Master of Science",
                "field_of_study": "Computer Science",
                "graduation_date": "2020-06-15",
                "min_grade": "3.5 GPA",
                "must_verify": True
            }
        ],
        "verification_requirements": {
            "education_verification_required": True,
            "employment_verification_required": False
        }
    },
    "validation_matrix": {
        "company_id": "UNIVERSITY_HIRING_001",
        "matrix_name": "Academic Verification Matrix",
        "education_rules": {
            "institution_name_rule": {
                "field_name": "institution_name",
                "field_type": "text",
                "match_type": "fuzzy",
                "similarity_threshold": 0.9,
                "green_threshold": 0.95,
                "amber_threshold": 0.8
            },
            "degree_rule": {
                "field_name": "degree",
                "field_type": "text",
                "match_type": "fuzzy",
                "similarity_threshold": 0.85,
                "green_threshold": 0.9,
                "amber_threshold": 0.7
            }
        }
    }
}

# Submit and process (same pattern as above)
```

### Example 3: Comprehensive Background Check

This example shows a complete background verification including employment, education, and personal information.

```python
comprehensive_request = {
    "enriched_cpf": {
        "candidate_id": "CAND_003",
        "declared_info": {
            "personal_info": {
                "full_name": "Carol Davis",
                "first_name": "Carol",
                "last_name": "Davis",
                "date_of_birth": "1985-03-20",
                "national_id": "987-65-4321",
                "email": "<EMAIL>",
                "phone": "******-987-6543"
            },
            "employment_history": [
                {
                    "employer_name": "Global Tech Solutions",
                    "job_title": "Engineering Manager",
                    "start_date": "2020-03-01",
                    "end_date": None,
                    "salary": 120000,
                    "currency": "USD"
                },
                {
                    "employer_name": "Innovation Labs",
                    "job_title": "Senior Software Engineer",
                    "start_date": "2017-06-15",
                    "end_date": "2020-02-28",
                    "salary": 95000,
                    "currency": "USD"
                }
            ],
            "education_history": [
                {
                    "institution_name": "MIT",
                    "degree": "Master of Science",
                    "field_of_study": "Computer Science",
                    "start_date": "2015-09-01",
                    "end_date": "2017-05-31",
                    "grade": "3.9 GPA"
                }
            ]
        },
        "government_verification": {
            "national_id_verified": True,
            "employment_verified": True,
            "education_verified": True
        }
    },
    "client_input": {
        "client_id": "ENTERPRISE_CORP_001",
        "candidate_id": "CAND_003",
        "job_position": "Senior Engineering Manager",
        "expected_personal_info": {
            "full_name": "Carol Davis",
            "date_of_birth": "1985-03-20",
            "national_id": "987-65-4321",
            "email": "<EMAIL>"
        },
        "expected_employment": [
            {
                "employer_name": "Global Tech Solutions",
                "job_title": "Engineering Manager",
                "start_date": "2020-03-01",
                "min_salary": 115000,
                "max_salary": 125000,
                "priority": "critical",
                "must_verify": True
            }
        ],
        "expected_education": [
            {
                "institution_name": "MIT",
                "degree": "Master of Science",
                "field_of_study": "Computer Science",
                "priority": "high",
                "must_verify": True
            }
        ],
        "verification_requirements": {
            "employment_verification_required": True,
            "education_verification_required": True,
            "identity_verification_required": True,
            "reference_check": True,
            "priority_level": "high"
        }
    },
    "validation_matrix": {
        "company_id": "ENTERPRISE_CORP_001",
        "matrix_name": "Executive Level Verification",
        "employment_rules": {
            "employer_name_rule": {
                "field_name": "employer_name",
                "field_type": "text",
                "match_type": "fuzzy",
                "similarity_threshold": 0.9,
                "priority": "critical",
                "green_threshold": 0.95,
                "amber_threshold": 0.8
            },
            "salary_rule": {
                "field_name": "salary",
                "field_type": "number",
                "match_type": "range",
                "numeric_tolerance_percent": 5.0,
                "priority": "high",
                "green_threshold": 0.9,
                "amber_threshold": 0.7
            }
        },
        "education_rules": {
            "institution_name_rule": {
                "field_name": "institution_name",
                "field_type": "text",
                "match_type": "fuzzy",
                "similarity_threshold": 0.9,
                "priority": "high",
                "green_threshold": 0.95,
                "amber_threshold": 0.8
            }
        },
        "personal_info_rules": {
            "name_rule": {
                "field_name": "name",
                "field_type": "text",
                "match_type": "fuzzy",
                "similarity_threshold": 0.95,
                "priority": "critical",
                "green_threshold": 0.98,
                "amber_threshold": 0.9
            },
            "national_id_rule": {
                "field_name": "national_id",
                "field_type": "text",
                "match_type": "exact",
                "priority": "critical",
                "green_threshold": 1.0,
                "amber_threshold": 0.0
            }
        },
        "global_green_threshold": 0.95,
        "global_amber_threshold": 0.8,
        "human_review_threshold": 0.7
    }
}
```

## Advanced Usage Patterns

### Batch Processing

Process multiple candidates simultaneously:

```python
import asyncio
import aiohttp

async def submit_verification(session, candidate_data):
    async with session.post(f"{api_url}/verify", json=candidate_data) as response:
        return await response.json()

async def process_batch(candidates):
    async with aiohttp.ClientSession() as session:
        tasks = [submit_verification(session, candidate) for candidate in candidates]
        results = await asyncio.gather(*tasks)
        return results

# Usage
candidates = [candidate1_data, candidate2_data, candidate3_data]
batch_results = asyncio.run(process_batch(candidates))
```

### Custom Validation Rules

Create industry-specific validation matrices:

```python
# Financial services validation matrix
financial_validation_matrix = {
    "company_id": "FINANCIAL_CORP_001",
    "matrix_name": "Financial Services Compliance",
    "employment_rules": {
        "employer_name_rule": {
            "field_name": "employer_name",
            "field_type": "text",
            "match_type": "exact",  # Stricter for financial sector
            "priority": "critical",
            "green_threshold": 1.0,
            "amber_threshold": 0.95
        }
    },
    "compliance_rules": {
        "sox_compliant": True,
        "audit_trail_required": True,
        "encrypt_sensitive_data": True
    },
    "global_green_threshold": 0.95,
    "global_amber_threshold": 0.85,
    "human_review_threshold": 0.8
}

# Healthcare validation matrix
healthcare_validation_matrix = {
    "company_id": "HEALTHCARE_CORP_001",
    "matrix_name": "Healthcare HIPAA Compliance",
    "employment_rules": {
        "employer_name_rule": {
            "field_name": "employer_name",
            "field_type": "text",
            "match_type": "fuzzy",
            "similarity_threshold": 0.9,
            "priority": "high"
        }
    },
    "compliance_rules": {
        "hipaa_compliant": True,
        "data_retention_days": 2555,  # 7 years
        "require_consent": True
    }
}
```

### Error Handling and Retry Logic

Implement robust error handling:

```python
import time
import random

def submit_verification_with_retry(request_data, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = requests.post(f"{api_url}/verify", json=request_data)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:  # Rate limited
                wait_time = 2 ** attempt + random.uniform(0, 1)
                print(f"Rate limited. Waiting {wait_time:.2f} seconds...")
                time.sleep(wait_time)
            elif response.status_code >= 500:  # Server error
                wait_time = 2 ** attempt
                print(f"Server error. Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                # Client error, don't retry
                response.raise_for_status()
                
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                raise
            wait_time = 2 ** attempt
            print(f"Request failed: {e}. Retrying in {wait_time} seconds...")
            time.sleep(wait_time)
    
    raise Exception(f"Failed to submit verification after {max_retries} attempts")
```

### Webhook Integration

Set up webhook notifications for completed verifications:

```python
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/webhook/verification-complete', methods=['POST'])
def handle_verification_complete():
    webhook_data = request.json
    
    job_id = webhook_data['job_id']
    candidate_id = webhook_data['candidate_id']
    status = webhook_data['status']
    
    if status == 'completed':
        result = webhook_data['result']
        
        # Process the verification result
        if result['verification_results']['overall_color_code'] == 'Green':
            # Approve candidate
            approve_candidate(candidate_id)
        elif result['verification_results']['requires_human_review']:
            # Flag for human review
            flag_for_review(candidate_id, result)
        
        # Update your system
        update_candidate_status(candidate_id, result)
    
    return jsonify({'status': 'received'})

def approve_candidate(candidate_id):
    # Your approval logic
    print(f"Candidate {candidate_id} approved")

def flag_for_review(candidate_id, result):
    # Your review flagging logic
    print(f"Candidate {candidate_id} flagged for review")

def update_candidate_status(candidate_id, result):
    # Update your database/system
    print(f"Updated status for candidate {candidate_id}")

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

## Integration Examples

### HRIS Integration

Integrate with Human Resource Information Systems:

```python
class HRISIntegration:
    def __init__(self, hris_api_url, verification_api_url):
        self.hris_api = hris_api_url
        self.verification_api = verification_api_url
    
    def verify_new_hire(self, employee_id):
        # Get candidate data from HRIS
        candidate_data = self.get_candidate_from_hris(employee_id)
        
        # Convert to verification format
        verification_request = self.convert_to_verification_format(candidate_data)
        
        # Submit verification
        response = requests.post(
            f"{self.verification_api}/verify",
            json=verification_request
        )
        job_id = response.json()['job_id']
        
        # Wait for completion and update HRIS
        result = self.wait_for_completion(job_id)
        self.update_hris_with_result(employee_id, result)
        
        return result
    
    def get_candidate_from_hris(self, employee_id):
        # Fetch from your HRIS system
        pass
    
    def convert_to_verification_format(self, candidate_data):
        # Convert HRIS format to verification format
        pass
    
    def wait_for_completion(self, job_id):
        # Poll for completion
        pass
    
    def update_hris_with_result(self, employee_id, result):
        # Update HRIS with verification results
        pass
```

### ATS Integration

Integrate with Applicant Tracking Systems:

```python
class ATSIntegration:
    def __init__(self, ats_webhook_url):
        self.ats_webhook = ats_webhook_url
    
    def process_candidate_application(self, application_data):
        # Extract verification data from application
        verification_request = self.build_verification_request(application_data)
        
        # Submit verification
        job_id = self.submit_verification(verification_request)
        
        # Store job ID with application
        self.link_job_to_application(application_data['id'], job_id)
        
        return job_id
    
    def handle_verification_result(self, job_id, result):
        # Find associated application
        application_id = self.get_application_by_job_id(job_id)
        
        # Update application status based on result
        if result['overall_color_code'] == 'Green':
            self.update_application_status(application_id, 'background_check_passed')
        elif result['requires_human_review']:
            self.update_application_status(application_id, 'background_check_review')
        else:
            self.update_application_status(application_id, 'background_check_failed')
```

## Testing Examples

### Unit Testing

```python
import pytest
from unittest.mock import Mock, patch

def test_verification_submission():
    with patch('requests.post') as mock_post:
        mock_post.return_value.json.return_value = {'job_id': 'test_job_123'}
        mock_post.return_value.status_code = 200
        
        result = submit_verification_request(sample_request)
        
        assert result['job_id'] == 'test_job_123'
        mock_post.assert_called_once()

def test_error_handling():
    with patch('requests.post') as mock_post:
        mock_post.side_effect = requests.exceptions.ConnectionError()
        
        with pytest.raises(Exception):
            submit_verification_request(sample_request)
```

### Integration Testing

```python
def test_end_to_end_verification():
    # Submit real verification request
    response = requests.post(f"{api_url}/verify", json=test_request)
    assert response.status_code == 200
    
    job_id = response.json()['job_id']
    
    # Wait for completion
    max_wait = 120  # 2 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        status_response = requests.get(f"{api_url}/status/{job_id}")
        status = status_response.json()['status']
        
        if status == 'completed':
            break
        elif status == 'failed':
            pytest.fail("Verification failed")
        
        time.sleep(5)
    else:
        pytest.fail("Verification timed out")
    
    # Check results
    results_response = requests.get(f"{api_url}/results/{job_id}")
    assert results_response.status_code == 200
    
    results = results_response.json()
    assert 'verification_results' in results
    assert 'overall_score' in results['verification_results']
```
