# Deployment Guide

## Overview

This guide covers deploying the ML Background Verification System in various environments, from development to production.

## Prerequisites

### System Requirements

- **Python**: 3.8 or higher
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: Minimum 10GB free space
- **CPU**: 2+ cores recommended
- **Network**: Internet access for LLM API calls

### Dependencies

- Docker (optional, for containerized deployment)
- PostgreSQL or SQLite (for data persistence)
- Redis (optional, for caching and job queues)
- <PERSON>in<PERSON> (optional, for reverse proxy)

## Environment Setup

### 1. Clone Repository

```bash
git clone <repository-url>
cd ml-poc
```

### 2. Create Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Environment Configuration

Copy the example environment file and configure:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.1

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/verification_db

# Security
SECRET_KEY=your_secret_key_here

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Verification Thresholds
CONFIDENCE_THRESHOLD_GREEN=0.9
CONFIDENCE_THRESHOLD_AMBER=0.7
HUMAN_REVIEW_THRESHOLD=0.6
```

## Development Deployment

### Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   python -m src.api.main
   ```

3. **Access the API:**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc

### Development with Auto-reload

```bash
uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src tests/

# Run specific test categories
pytest -m unit
pytest -m integration
pytest -m api
```

## Production Deployment

### Option 1: Direct Python Deployment

#### 1. System Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3.8 python3.8-venv python3.8-dev -y
sudo apt install postgresql postgresql-contrib nginx -y
```

#### 2. Application Setup

```bash
# Create application user
sudo useradd -m -s /bin/bash verification
sudo su - verification

# Clone and setup application
git clone <repository-url> /home/<USER>/app
cd /home/<USER>/app
python3.8 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 3. Database Setup

```bash
# Create database
sudo -u postgres createdb verification_db
sudo -u postgres createuser verification_user
sudo -u postgres psql -c "ALTER USER verification_user PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE verification_db TO verification_user;"
```

#### 4. Systemd Service

Create `/etc/systemd/system/verification-api.service`:

```ini
[Unit]
Description=ML Background Verification API
After=network.target

[Service]
Type=exec
User=verification
Group=verification
WorkingDirectory=/home/<USER>/app
Environment=PATH=/home/<USER>/app/venv/bin
ExecStart=/home/<USER>/app/venv/bin/uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl daemon-reload
sudo systemctl enable verification-api
sudo systemctl start verification-api
sudo systemctl status verification-api
```

#### 5. Nginx Configuration

Create `/etc/nginx/sites-available/verification-api`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/verification-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile

```dockerfile
FROM python:3.8-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 verification && chown -R verification:verification /app
USER verification

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 2. Create docker-compose.yml

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************/verification_db
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=verification_db
      - POSTGRES_USER=verification
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 3. Deploy with Docker Compose

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f api

# Scale API service
docker-compose up -d --scale api=3
```

### Option 3: Kubernetes Deployment

#### 1. Create Kubernetes Manifests

**deployment.yaml:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: verification-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: verification-api
  template:
    metadata:
      labels:
        app: verification-api
    spec:
      containers:
      - name: api
        image: verification-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: verification-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: verification-secrets
              key: openai-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

**service.yaml:**
```yaml
apiVersion: v1
kind: Service
metadata:
  name: verification-api-service
spec:
  selector:
    app: verification-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

#### 2. Deploy to Kubernetes

```bash
# Create secrets
kubectl create secret generic verification-secrets \
  --from-literal=database-url="postgresql://..." \
  --from-literal=openai-api-key="sk-..."

# Deploy application
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml

# Check status
kubectl get pods
kubectl get services
```

## Monitoring and Logging

### 1. Application Logs

Logs are written to:
- Console (development)
- `/app/logs/api.log` (production)

### 2. Health Monitoring

Monitor the health endpoint:
```bash
curl http://localhost:8000/health
```

### 3. Metrics Collection

The application exposes metrics for monitoring:
- Request counts
- Response times
- Error rates
- Active jobs

### 4. Log Aggregation

For production, consider using:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Fluentd
- Prometheus + Grafana

## Security Considerations

### 1. API Security

- Implement authentication (JWT, API keys)
- Use HTTPS in production
- Set up rate limiting
- Validate all inputs
- Sanitize outputs

### 2. Data Security

- Encrypt sensitive data at rest
- Use secure database connections
- Implement data retention policies
- Regular security audits

### 3. Infrastructure Security

- Keep systems updated
- Use firewalls
- Implement network segmentation
- Regular vulnerability scans

## Backup and Recovery

### 1. Database Backups

```bash
# PostgreSQL backup
pg_dump verification_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump verification_db | gzip > $BACKUP_DIR/verification_backup_$DATE.sql.gz
find $BACKUP_DIR -name "verification_backup_*.sql.gz" -mtime +7 -delete
```

### 2. Application Backups

- Code repository (Git)
- Configuration files
- Log files
- SSL certificates

### 3. Recovery Procedures

Document and test recovery procedures:
1. Database restoration
2. Application deployment
3. Configuration restoration
4. Service restart

## Performance Optimization

### 1. Application Optimization

- Use connection pooling
- Implement caching (Redis)
- Optimize database queries
- Use async processing for long tasks

### 2. Infrastructure Optimization

- Load balancing
- CDN for static assets
- Database optimization
- Monitoring and alerting

### 3. Scaling Strategies

- Horizontal scaling (multiple instances)
- Vertical scaling (more resources)
- Database sharding
- Microservices architecture

## Troubleshooting

### Common Issues

1. **Service won't start:**
   - Check logs: `journalctl -u verification-api`
   - Verify configuration
   - Check port availability

2. **Database connection errors:**
   - Verify database is running
   - Check connection string
   - Verify credentials

3. **High memory usage:**
   - Monitor with `htop` or `ps`
   - Check for memory leaks
   - Adjust worker processes

4. **Slow responses:**
   - Check database performance
   - Monitor API response times
   - Review LLM API latency

### Log Analysis

```bash
# View recent logs
tail -f /app/logs/api.log

# Search for errors
grep "ERROR" /app/logs/api.log

# Monitor real-time requests
tail -f /app/logs/api.log | grep "Request"
```
