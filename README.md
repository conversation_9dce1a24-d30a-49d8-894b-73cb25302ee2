# ML-Powered Candidate Background Verification System

## Overview

This project implements an AI-powered automation system for candidate background verification. The system uses structured prompt engineering and machine learning algorithms to evaluate candidate information against client expectations and validation rules.

## Key Features

- **Multi-Input Processing**: Handles Enriched CPF, Client Input, and Validation Matrix
- **Intelligent Verification**: Uses structured prompt engineering for accurate data comparison
- **Color-Coded Results**: Green/Amber/Red classification system
- **Confidence Scoring**: Provides confidence levels and flags for human review
- **Comprehensive Validation**: Covers employment dates, employer names, salary, location, and identification fields

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Enriched CPF  │    │  Client Input   │    │ Validation      │
│   (Candidate +  │    │  (Expected      │    │ Matrix          │
│   Govt Verified)│    │   Values)       │    │ (Company Rules) │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   AI Verification Engine  │
                    │   (Prompt Engineering +   │
                    │    ML Algorithms)         │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Verification Output   │
                    │   • Color Codes           │
                    │   • Human-readable        │
                    │     Remarks               │
                    │   • Confidence Scores     │
                    │   • Review Flags          │
                    └───────────────────────────┘
```

## Project Structure

```
ml-poc/
├── src/
│   ├── models/              # Data models and schemas
│   ├── verification/        # Core verification engine
│   ├── prompt_engineering/  # Structured prompts and templates
│   ├── scoring/            # Classification and scoring logic
│   └── api/                # REST API endpoints
├── tests/                  # Unit and integration tests
├── data/                   # Sample data and test cases
├── config/                 # Configuration files
├── docs/                   # Documentation
└── requirements.txt        # Python dependencies
```

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the API Server**
   ```bash
   python -m src.api.main
   ```

3. **Test the System**
   ```bash
   pytest tests/
   ```

## Input Data Formats

### 1. Enriched CPF (Candidate Profile File)
Contains candidate-declared and government-verified information:
- Personal details (name, ID, address)
- Employment history
- Educational background
- Government verification status

### 2. Client Input
Contains expected values for verification:
- Expected employment dates
- Expected employer names
- Expected salary ranges
- Expected locations
- Required identification fields

### 3. Validation Matrix
Contains company-specific comparison rules:
- Field matching criteria
- Tolerance levels for dates and amounts
- Priority weights for different fields
- Custom validation rules

## Output Format

The system generates structured output for each verification point:

```json
{
  "field_name": "employment_date",
  "color_code": "Green|Amber|Red",
  "remark": "Human-readable explanation",
  "confidence_score": 0.95,
  "requires_human_review": false,
  "details": {
    "expected": "2020-01-01",
    "actual": "2020-01-15",
    "variance": "14 days"
  }
}
```

## Color Code System

- **Green**: Perfect match or within acceptable tolerance
- **Amber**: Minor discrepancies that may need attention
- **Red**: Significant discrepancies requiring investigation

## Development

### Prerequisites
- Python 3.8+
- Virtual environment recommended

### Setup Development Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### Running Tests
```bash
pytest tests/ -v
```

## API Documentation

The system provides RESTful API endpoints for integration:

- `POST /verify` - Submit verification request
- `GET /status/{job_id}` - Check verification status
- `GET /results/{job_id}` - Retrieve verification results

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is proprietary software for Digiverify background verification services.
