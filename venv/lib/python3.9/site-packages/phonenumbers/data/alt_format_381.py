"""Auto-generated file, do not edit by hand. 381 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_381 = [NumberFormat(pattern='(\\d{2})(\\d{3})(\\d{3})', format='\\1 \\2 \\3', leading_digits_pattern=['[16]|2[0-24-7]|3[0-8]|(?:2[389]|39)[2-9]']), NumberFormat(pattern='(\\d{2})(\\d{2})(\\d{2})(\\d{3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['1|2[0-24-7]|3[0-8]|(?:2[389]|39)[2-9]']), NumberFormat(pattern='(\\d{2})(\\d{3})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['6'])]
