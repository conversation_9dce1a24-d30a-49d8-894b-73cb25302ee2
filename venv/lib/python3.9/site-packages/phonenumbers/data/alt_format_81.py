"""Auto-generated file, do not edit by hand. 81 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_81 = [NumberFormat(pattern='(\\d{3})(\\d{2})(\\d{4})', format='\\1 \\2 \\3', leading_digits_pattern=['(?:12|57|99)0']), NumberFormat(pattern='(\\d{3})(\\d{2})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['(?:12|57|99)0']), NumberFormat(pattern='(\\d{3})(\\d{4})(\\d{2})', format='\\1 \\2 \\3', leading_digits_pattern=['(?:12|57|99)0'])]
