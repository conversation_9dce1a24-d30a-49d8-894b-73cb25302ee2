"""Auto-generated file, do not edit by hand. 44 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_44 = [NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{4})', format='\\1 \\2 \\3', leading_digits_pattern=['20']), NumberFormat(pattern='(\\d{3})(\\d{4})(\\d{3})', format='\\1 \\2 \\3', leading_digits_pattern=['1(?:1|[2-69]1)|20|[389]|7(?:[1-57-9]|624)']), NumberFormat(pattern='(\\d{2})(\\d{4})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['20']), NumberFormat(pattern='(\\d{4})(\\d{3})(\\d{3})', format='\\1 \\2 \\3', leading_digits_pattern=['1(?:[2-69][02-9]|[78])|3|7(?:[1-57-9]|624)']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{3})', format='\\1 \\2 \\3', leading_digits_pattern=['8'])]
