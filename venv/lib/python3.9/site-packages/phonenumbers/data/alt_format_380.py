"""Auto-generated file, do not edit by hand. 380 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_380 = [NumberFormat(pattern='(\\d{2})(\\d{3})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['[38]9|4[45][0-5]|5(?:0|6(?:3[14-7]|7))|6(?:[12][018]|[36-8])|7|9[1-9]|(?:48|57)[0137-9]']), NumberFormat(pattern='(\\d{2})(\\d{2})(\\d{3})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['[38]9|4[45][0-5]|5(?:0|6(?:3[14-7]|7))|6(?:[12][018]|[36-8])|7|9[1-9]|(?:48|57)[0137-9]'])]
