"""Auto-generated file, do not edit by hand. 61 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_61 = [NumberFormat(pattern='(\\d{4})(\\d{2})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4'), NumberFormat(pattern='(\\d{4})(\\d{6})', format='\\1 \\2'), NumberFormat(pattern='(\\d)(\\d{3})(\\d{3})(\\d{3})', format='\\1 \\2 \\3 \\4'), NumberFormat(pattern='(\\d)(\\d{8})', format='\\1 \\2', leading_digits_pattern=['[2378]'])]
