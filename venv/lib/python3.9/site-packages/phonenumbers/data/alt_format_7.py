"""Auto-generated file, do not edit by hand. 7 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_7 = [NumberFormat(pattern='(\\d{4})(\\d{3})(\\d{3})', format='\\1 \\2 \\3', leading_digits_pattern=['[3489]|7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:1[23]|[2-9]2))']), NumberFormat(pattern='(\\d{5})(\\d{5})', format='\\1 \\2', leading_digits_pattern=['[3489]|72(?:6|7[457])|7(?:12|2[49])[35]|7(?:1[13-58]|2[1-38])[3-5]|7(?:1[06]|25)[3-6]']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['7(?:1|2(?:[1-689]|7[2457]))']), NumberFormat(pattern='(\\d{3})(\\d{2})(\\d{2})(\\d{3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['[3489]|7(?:[04-9]|1(?:04|[236]3|4[3-5]|5[34])|2(?:13|34|7[39]))']), NumberFormat(pattern='(\\d{3})(\\d)(\\d{2})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4 \\5', leading_digits_pattern=['[3489]|7(?:[04-9]|1(?:04|[236]3|4[3-5]|5[34])|2(?:13|34|7[39]))']), NumberFormat(pattern='(\\d{4})(\\d{2})(\\d{2})(\\d{2})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['[3489]'])]
