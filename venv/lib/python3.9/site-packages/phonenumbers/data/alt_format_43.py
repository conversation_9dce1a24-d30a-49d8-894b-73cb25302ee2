"""Auto-generated file, do not edit by hand. 43 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_43 = [NumberFormat(pattern='(\\d)(\\d{3})(\\d{2})(\\d{2,3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['1']), NumberFormat(pattern='(\\d)(\\d{4,6})', format='\\1 \\2', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d)(\\d{7,8})', format='\\1 \\2', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d{2})(\\d{2})(\\d{2})(\\d{2,3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d{2})(\\d{6,7})', format='\\1 \\2', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d)(\\d{9,12})', format='\\1 \\2', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d{2})(\\d{2})(\\d{2})(\\d{4})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2,4})', format='\\1 \\2 \\3 \\4 \\5', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d{2})(\\d{5})(\\d{4,6})', format='\\1 \\2 \\3', leading_digits_pattern=['5[079]']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{3})(\\d{3,4})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['(?:31|4)6|51|6(?:485|5[0-3579]|[6-9])|7(?:20|32|8)|[89]']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{2})(\\d{2,3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['(?:31|4)6|51|6(?:485|5[0-3579]|[6-9])|7(?:20|32|8)|[89]']), NumberFormat(pattern='(\\d{3})(\\d{2})(\\d{2})(\\d{2,3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['(?:31|4)6|51|6(?:485|5[0-3579]|[6-9])|7(?:20|32|8)|[89]']), NumberFormat(pattern='(\\d{4})(\\d{3})(\\d{3,4})', format='\\1 \\2 \\3', leading_digits_pattern=['2|3(?:1[1-578]|[3-68])|4[2378]|5[2-6]|6(?:[12]|4(?:[135-7]|8[34])|5[468])|7(?:2[1-8]|35|[4-79])'])]
