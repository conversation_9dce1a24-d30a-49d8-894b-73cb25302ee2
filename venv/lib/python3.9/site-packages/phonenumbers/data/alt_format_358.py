"""Auto-generated file, do not edit by hand. 358 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_358 = [NumberFormat(pattern='(\\d)(\\d{3})(\\d{3,4})', format='\\1 \\2 \\3', leading_digits_pattern=['[2568][1-8]|3(?:0[1-9]|[1-9])|9']), NumberFormat(pattern='(\\d{2})(\\d{3})(\\d{3,4})', format='\\1 \\2 \\3', leading_digits_pattern=['[12]0[1-9]|4|1[3-9]|29|50|7[15]']), NumberFormat(pattern='(\\d)(\\d{4})(\\d{3})', format='\\1 \\2 \\3', leading_digits_pattern=['[2568][1-8]|3(?:0[1-9]|[1-9])|9'])]
