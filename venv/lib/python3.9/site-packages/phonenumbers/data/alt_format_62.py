"""Auto-generated file, do not edit by hand. 62 metadata"""
from ..phonemetadata import NumberFormat

PHONE_ALT_FORMAT_62 = [NumberFormat(pattern='(\\d{2})(\\d{3,4})(\\d{4})', format='\\1 \\2 \\3', leading_digits_pattern=['2[124]|[36]1']), NumberFormat(pattern='(\\d{2})(\\d{3})(\\d{5})', format='\\1 \\2 \\3', leading_digits_pattern=['2[124]|[36]1']), NumberFormat(pattern='(\\d{2})(\\d{2})(\\d{3})(\\d{3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['2[124]|[36]1']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{4})', format='\\1 \\2 \\3', leading_digits_pattern=['8[1-35-9]']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{5,6})', format='\\1 \\2 \\3', leading_digits_pattern=['8']), NumberFormat(pattern='(\\d{3})(\\d{3})(\\d{2})(\\d{3})', format='\\1 \\2 \\3 \\4', leading_digits_pattern=['8'])]
