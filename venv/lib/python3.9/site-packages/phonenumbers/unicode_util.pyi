from .util import UnicodeMixin

class Category:
    LETTER: str
    UPPERCASE_LETTER: str
    LOWERCASE_LETTER: str
    TITLECASE_LETTER: str
    MODIFIER_LETTER: str
    OTHER_LETTER: str
    MARK: str
    NON_SPACING_MARK: str
    SPACING_COMBINING_MARK: str
    ENCLOSING_MARK: str
    NUMBER: str
    DECIMAL_DIGIT_NUMBER: str
    LETTER_NUMBER: str
    OTHER_NUMBER: str
    SYMBOL: str
    MATH_SYMBOL: str
    CURRENCY_SYMBOL: str
    MODIFIER_SYMBOL: str
    OTHER_SYMBOL: str
    PUNCTUATION: str
    CONNECTOR_PUNCTUATION: str
    DASH_PUNCTUATION: str
    OPEN_PUNCTUATION: str
    CLOSE_PUNCTUATION: str
    INITIAL_PUNCTUATION: str
    FINAL_PUNCTUATION: str
    OTHER_PUNCTUATION: str
    SEPARATOR: str
    SPACE_SEPARATOR: str
    LINE_SEPARATOR: str
    PARAGRAPH_SEPARATOR: str
    OTHER: str
    CONTROL: str
    FORMAT: str
    SURROGATE: str
    PRIVATE_USE: str
    NOT_ASSIGNED: str
    @classmethod
    def get(cls, uni_char: str) -> str: ...

def is_letter(uni_char: str) -> bool: ...

class _BlockRange(UnicodeMixin):
    start: int
    end: int
    def __init__(self, start: int, end: int, regdict: dict[int, _BlockRange] | None = ...) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __unicode__(self) -> str: ...

class Block:
    _RANGES: dict[int, _BlockRange]
    _RANGE_KEYS: list[int] | None
    BASIC_LATIN: _BlockRange
    LATIN_1_SUPPLEMENT: _BlockRange
    LATIN_EXTENDED_A: _BlockRange
    LATIN_EXTENDED_B: _BlockRange
    IPA_EXTENSIONS: _BlockRange
    SPACING_MODIFIER_LETTERS: _BlockRange
    COMBINING_DIACRITICAL_MARKS: _BlockRange
    GREEK_AND_COPTIC: _BlockRange
    CYRILLIC: _BlockRange
    CYRILLIC_SUPPLEMENT: _BlockRange
    ARMENIAN: _BlockRange
    HEBREW: _BlockRange
    ARABIC: _BlockRange
    SYRIAC: _BlockRange
    ARABIC_SUPPLEMENT: _BlockRange
    THAANA: _BlockRange
    NKO: _BlockRange
    SAMARITAN: _BlockRange
    MANDAIC: _BlockRange
    DEVANAGARI: _BlockRange
    BENGALI: _BlockRange
    GURMUKHI: _BlockRange
    GUJARATI: _BlockRange
    ORIYA: _BlockRange
    TAMIL: _BlockRange
    TELUGU: _BlockRange
    KANNADA: _BlockRange
    MALAYALAM: _BlockRange
    SINHALA: _BlockRange
    THAI: _BlockRange
    LAO: _BlockRange
    TIBETAN: _BlockRange
    MYANMAR: _BlockRange
    GEORGIAN: _BlockRange
    HANGUL_JAMO: _BlockRange
    ETHIOPIC: _BlockRange
    ETHIOPIC_SUPPLEMENT: _BlockRange
    CHEROKEE: _BlockRange
    UNIFIED_CANADIAN_ABORIGINAL_SYLLABICS: _BlockRange
    OGHAM: _BlockRange
    RUNIC: _BlockRange
    TAGALOG: _BlockRange
    HANUNOO: _BlockRange
    BUHID: _BlockRange
    TAGBANWA: _BlockRange
    KHMER: _BlockRange
    MONGOLIAN: _BlockRange
    UNIFIED_CANADIAN_ABORIGINAL_SYLLABICS_EXTENDED: _BlockRange
    LIMBU: _BlockRange
    TAI_LE: _BlockRange
    NEW_TAI_LUE: _BlockRange
    KHMER_SYMBOLS: _BlockRange
    BUGINESE: _BlockRange
    TAI_THAM: _BlockRange
    BALINESE: _BlockRange
    SUNDANESE: _BlockRange
    BATAK: _BlockRange
    LEPCHA: _BlockRange
    OL_CHIKI: _BlockRange
    VEDIC_EXTENSIONS: _BlockRange
    PHONETIC_EXTENSIONS: _BlockRange
    PHONETIC_EXTENSIONS_SUPPLEMENT: _BlockRange
    COMBINING_DIACRITICAL_MARKS_SUPPLEMENT: _BlockRange
    LATIN_EXTENDED_ADDITIONAL: _BlockRange
    GREEK_EXTENDED: _BlockRange
    GENERAL_PUNCTUATION: _BlockRange
    SUPERSCRIPTS_AND_SUBSCRIPTS: _BlockRange
    CURRENCY_SYMBOLS: _BlockRange
    COMBINING_DIACRITICAL_MARKS_FOR_SYMBOLS: _BlockRange
    LETTERLIKE_SYMBOLS: _BlockRange
    NUMBER_FORMS: _BlockRange
    ARROWS: _BlockRange
    MATHEMATICAL_OPERATORS: _BlockRange
    MISCELLANEOUS_TECHNICAL: _BlockRange
    CONTROL_PICTURES: _BlockRange
    OPTICAL_CHARACTER_RECOGNITION: _BlockRange
    ENCLOSED_ALPHANUMERICS: _BlockRange
    BOX_DRAWING: _BlockRange
    BLOCK_ELEMENTS: _BlockRange
    GEOMETRIC_SHAPES: _BlockRange
    MISCELLANEOUS_SYMBOLS: _BlockRange
    DINGBATS: _BlockRange
    MISCELLANEOUS_MATHEMATICAL_SYMBOLS_A: _BlockRange
    SUPPLEMENTAL_ARROWS_A: _BlockRange
    BRAILLE_PATTERNS: _BlockRange
    SUPPLEMENTAL_ARROWS_B: _BlockRange
    MISCELLANEOUS_MATHEMATICAL_SYMBOLS_B: _BlockRange
    SUPPLEMENTAL_MATHEMATICAL_OPERATORS: _BlockRange
    MISCELLANEOUS_SYMBOLS_AND_ARROWS: _BlockRange
    GLAGOLITIC: _BlockRange
    LATIN_EXTENDED_C: _BlockRange
    COPTIC: _BlockRange
    GEORGIAN_SUPPLEMENT: _BlockRange
    TIFINAGH: _BlockRange
    ETHIOPIC_EXTENDED: _BlockRange
    CYRILLIC_EXTENDED_A: _BlockRange
    SUPPLEMENTAL_PUNCTUATION: _BlockRange
    CJK_RADICALS_SUPPLEMENT: _BlockRange
    KANGXI_RADICALS: _BlockRange
    IDEOGRAPHIC_DESCRIPTION_CHARACTERS: _BlockRange
    CJK_SYMBOLS_AND_PUNCTUATION: _BlockRange
    HIRAGANA: _BlockRange
    KATAKANA: _BlockRange
    BOPOMOFO: _BlockRange
    HANGUL_COMPATIBILITY_JAMO: _BlockRange
    KANBUN: _BlockRange
    BOPOMOFO_EXTENDED: _BlockRange
    CJK_STROKES: _BlockRange
    KATAKANA_PHONETIC_EXTENSIONS: _BlockRange
    ENCLOSED_CJK_LETTERS_AND_MONTHS: _BlockRange
    CJK_COMPATIBILITY: _BlockRange
    CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A: _BlockRange
    YIJING_HEXAGRAM_SYMBOLS: _BlockRange
    CJK_UNIFIED_IDEOGRAPHS: _BlockRange
    YI_SYLLABLES: _BlockRange
    YI_RADICALS: _BlockRange
    LISU: _BlockRange
    VAI: _BlockRange
    CYRILLIC_EXTENDED_B: _BlockRange
    BAMUM: _BlockRange
    MODIFIER_TONE_LETTERS: _BlockRange
    LATIN_EXTENDED_D: _BlockRange
    SYLOTI_NAGRI: _BlockRange
    COMMON_INDIC_NUMBER_FORMS: _BlockRange
    PHAGS_PA: _BlockRange
    SAURASHTRA: _BlockRange
    DEVANAGARI_EXTENDED: _BlockRange
    KAYAH_LI: _BlockRange
    REJANG: _BlockRange
    HANGUL_JAMO_EXTENDED_A: _BlockRange
    JAVANESE: _BlockRange
    CHAM: _BlockRange
    MYANMAR_EXTENDED_A: _BlockRange
    TAI_VIET: _BlockRange
    ETHIOPIC_EXTENDED_A: _BlockRange
    MEETEI_MAYEK: _BlockRange
    HANGUL_SYLLABLES: _BlockRange
    HANGUL_JAMO_EXTENDED_B: _BlockRange
    HIGH_SURROGATES: _BlockRange
    HIGH_PRIVATE_USE_SURROGATES: _BlockRange
    LOW_SURROGATES: _BlockRange
    PRIVATE_USE_AREA: _BlockRange
    CJK_COMPATIBILITY_IDEOGRAPHS: _BlockRange
    ALPHABETIC_PRESENTATION_FORMS: _BlockRange
    ARABIC_PRESENTATION_FORMS_A: _BlockRange
    VARIATION_SELECTORS: _BlockRange
    VERTICAL_FORMS: _BlockRange
    COMBINING_HALF_MARKS: _BlockRange
    CJK_COMPATIBILITY_FORMS: _BlockRange
    SMALL_FORM_VARIANTS: _BlockRange
    ARABIC_PRESENTATION_FORMS_B: _BlockRange
    HALFWIDTH_AND_FULLWIDTH_FORMS: _BlockRange
    SPECIALS: _BlockRange
    LINEAR_B_SYLLABARY: _BlockRange
    LINEAR_B_IDEOGRAMS: _BlockRange
    AEGEAN_NUMBERS: _BlockRange
    ANCIENT_GREEK_NUMBERS: _BlockRange
    ANCIENT_SYMBOLS: _BlockRange
    PHAISTOS_DISC: _BlockRange
    LYCIAN: _BlockRange
    CARIAN: _BlockRange
    OLD_ITALIC: _BlockRange
    GOTHIC: _BlockRange
    UGARITIC: _BlockRange
    OLD_PERSIAN: _BlockRange
    DESERET: _BlockRange
    SHAVIAN: _BlockRange
    OSMANYA: _BlockRange
    CYPRIOT_SYLLABARY: _BlockRange
    IMPERIAL_ARAMAIC: _BlockRange
    PHOENICIAN: _BlockRange
    LYDIAN: _BlockRange
    KHAROSHTHI: _BlockRange
    OLD_SOUTH_ARABIAN: _BlockRange
    AVESTAN: _BlockRange
    INSCRIPTIONAL_PARTHIAN: _BlockRange
    INSCRIPTIONAL_PAHLAVI: _BlockRange
    OLD_TURKIC: _BlockRange
    RUMI_NUMERAL_SYMBOLS: _BlockRange
    BRAHMI: _BlockRange
    KAITHI: _BlockRange
    CUNEIFORM: _BlockRange
    CUNEIFORM_NUMBERS_AND_PUNCTUATION: _BlockRange
    EGYPTIAN_HIEROGLYPHS: _BlockRange
    BAMUM_SUPPLEMENT: _BlockRange
    KANA_SUPPLEMENT: _BlockRange
    BYZANTINE_MUSICAL_SYMBOLS: _BlockRange
    MUSICAL_SYMBOLS: _BlockRange
    ANCIENT_GREEK_MUSICAL_NOTATION: _BlockRange
    TAI_XUAN_JING_SYMBOLS: _BlockRange
    COUNTING_ROD_NUMERALS: _BlockRange
    MATHEMATICAL_ALPHANUMERIC_SYMBOLS: _BlockRange
    MAHJONG_TILES: _BlockRange
    DOMINO_TILES: _BlockRange
    PLAYING_CARDS: _BlockRange
    ENCLOSED_ALPHANUMERIC_SUPPLEMENT: _BlockRange
    ENCLOSED_IDEOGRAPHIC_SUPPLEMENT: _BlockRange
    MISCELLANEOUS_SYMBOLS_AND_PICTOGRAPHS: _BlockRange
    EMOTICONS: _BlockRange
    TRANSPORT_AND_MAP_SYMBOLS: _BlockRange
    ALCHEMICAL_SYMBOLS: _BlockRange
    CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B: _BlockRange
    CJK_UNIFIED_IDEOGRAPHS_EXTENSION_C: _BlockRange
    CJK_UNIFIED_IDEOGRAPHS_EXTENSION_D: _BlockRange
    CJK_COMPATIBILITY_IDEOGRAPHS_SUPPLEMENT: _BlockRange
    TAGS: _BlockRange
    VARIATION_SELECTORS_SUPPLEMENT: _BlockRange
    SUPPLEMENTARY_PRIVATE_USE_AREA_A: _BlockRange
    SUPPLEMENTARY_PRIVATE_USE_AREA_B: _BlockRange
    UNKNOWN: _BlockRange
    @classmethod
    def get(cls, uni_char: str) -> _BlockRange: ...

def digit(uni_char: str, default_value: int | None = ...) -> int: ...
