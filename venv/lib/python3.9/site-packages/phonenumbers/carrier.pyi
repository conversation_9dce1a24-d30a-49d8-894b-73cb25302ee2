from .phonenumber import PhoneNumber

__all__: list[str]

def name_for_valid_number(numobj: PhoneNumber, lang: str, script: str | None = ..., region: str | None = ...) -> str: ...
def name_for_number(numobj: PhoneNumber, lang: str, script: str | None = ..., region: str | None = ...) -> str: ...
def safe_display_name(numobj: PhoneNumber, lang: str, script: str | None = ..., region: str | None = ...) -> str: ...
def _is_mobile(ntype: int) -> bool: ...
