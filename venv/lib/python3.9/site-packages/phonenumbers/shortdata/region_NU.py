"""Auto-generated file, do not edit by hand. NU metadata"""
from ..phonemetadata import NumberFormat, PhoneNumberDesc, PhoneMetadata

PHONE_METADATA_NU = PhoneMetadata(id='NU', country_code=None, international_prefix=None,
    general_desc=PhoneNumberDesc(national_number_pattern='[019]\\d\\d', possible_length=(3,)),
    toll_free=PhoneNumberDesc(national_number_pattern='999', example_number='999', possible_length=(3,)),
    emergency=PhoneNumberDesc(national_number_pattern='999', example_number='999', possible_length=(3,)),
    short_code=PhoneNumberDesc(national_number_pattern='01[05]|101|999', example_number='010', possible_length=(3,)),
    carrier_specific=PhoneNumberDesc(national_number_pattern='010', example_number='010', possible_length=(3,)),
    short_data=True)
