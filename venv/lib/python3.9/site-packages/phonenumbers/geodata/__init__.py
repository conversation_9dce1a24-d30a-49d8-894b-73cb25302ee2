"""Per-prefix data, mapping each prefix to a dict of locale:name.

Auto-generated file, do not edit by hand.
"""
from ..util import u

# Copyright (C) 2011-2025 The Libphonenumber Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

GEOCODE_DATA = {}
from .data0 import data
GEOCODE_DATA.update(data)
from .data1 import data
GEOCODE_DATA.update(data)
from .data2 import data
GEOCODE_DATA.update(data)
from .data3 import data
GEOCODE_DATA.update(data)
from .data4 import data
GEOCODE_DATA.update(data)
from .data5 import data
GEOCODE_DATA.update(data)
from .data6 import data
GEOCODE_DATA.update(data)
from .data7 import data
GEOCODE_DATA.update(data)
from .data8 import data
GEOCODE_DATA.update(data)
from .data9 import data
GEOCODE_DATA.update(data)
from .data10 import data
GEOCODE_DATA.update(data)
from .data11 import data
GEOCODE_DATA.update(data)
from .data12 import data
GEOCODE_DATA.update(data)
from .data13 import data
GEOCODE_DATA.update(data)
from .data14 import data
GEOCODE_DATA.update(data)
from .data15 import data
GEOCODE_DATA.update(data)
from .data16 import data
GEOCODE_DATA.update(data)
from .data17 import data
GEOCODE_DATA.update(data)
from .data18 import data
GEOCODE_DATA.update(data)
from .data19 import data
GEOCODE_DATA.update(data)
from .data20 import data
GEOCODE_DATA.update(data)
from .data21 import data
GEOCODE_DATA.update(data)
from .data22 import data
GEOCODE_DATA.update(data)
from .data23 import data
GEOCODE_DATA.update(data)
from .data24 import data
GEOCODE_DATA.update(data)
from .data25 import data
GEOCODE_DATA.update(data)
from .data26 import data
GEOCODE_DATA.update(data)
from .data27 import data
GEOCODE_DATA.update(data)
from .data28 import data
GEOCODE_DATA.update(data)
del data
GEOCODE_LONGEST_PREFIX = 9
